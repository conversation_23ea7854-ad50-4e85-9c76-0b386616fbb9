# LegacyBridge Docker Build Exclusions
# Optimized for multi-stage builds and security

# Version control
.git
.gitignore
.gitattributes
.github

# Documentation
*.md
!README.md
docs/
*.txt
*.rst
LICENSE*
CHANGELOG*

# Development files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn
.pnpm-debug.log*

# Build outputs (will be built inside container)
dist/
build/
out/
.next/
target/
*.tsbuildinfo

# Test files and coverage
test/
tests/
__tests__/
*.test.js
*.test.ts
*.spec.js
*.spec.ts
coverage/
.nyc_output/
junit.xml
test-results/
playwright-report/

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
.nyc_output/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# Rust specific
target/
Cargo.lock
**/*.rs.bk

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/

# IDEs and editors
.vscode/
.idea/
*.sublime-project
*.sublime-workspace
.atom/
.brackets.json

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
tmp/
temp/
*.tmp
*.temp

# Archives
*.zip
*.tar.gz
*.rar
*.7z

# Database files
*.db
*.sqlite
*.sqlite3

# Configuration files that shouldn't be in container
docker-compose*.yml
Dockerfile*
.dockerignore

# CI/CD files
.github/
.gitlab-ci.yml
.travis.yml
.circleci/
azure-pipelines.yml
Jenkinsfile

# Kubernetes and deployment
k8s/
helm/
terraform/
*.yaml
*.yml
!package.json
!tsconfig.json
!next.config.ts

# Security and secrets
.env*
secrets/
*.pem
*.key
*.crt
*.p12
*.pfx

# Performance and profiling
*.cpuprofile
*.heapprofile
*.heapsnapshot

# Backup files
*.bak
*.backup
*.old

# Editor specific
*.swp
*.swo
*~

# Package manager locks (will be generated in container)
package-lock.json
yarn.lock
pnpm-lock.yaml

# Build tools
webpack.config.js
rollup.config.js
vite.config.js
.babelrc
.eslintrc*
.prettierrc*
jest.config.js
playwright.config.ts

# Monitoring and analytics
.lighthouseci/
.nyc_output/

# Local development
.local/
local/

# Cache directories
.cache/
.parcel-cache/
.eslintcache
.stylelintcache

# Runtime directories that will be created in container
uploads/
output/
storage/

# Development scripts
scripts/dev/
scripts/test/

# Example and demo files
examples/
demo/
samples/

# Handoff and documentation files
handoffs/
memory-bank/
*.handoff.md
HANDOFF_*.md
augment-*.md
CURSOR-*.md

# Performance test files
perf_test/
performance_test/
load_test/

# Legacy and backup directories
legacy-bridge-main/
legacy-bridge.zip
*.backup

# Validation and test scripts
validation_tests/
integration_test_suite.sh
quick_validation_test.sh
test_*.sh

# Build artifacts that will be recreated
dll-build/
deployment_package/
ENTERPRISE_PACKAGE/

# Specific to this project
firebase-debug.log
utputFormat
simd_performance_report.md
build_test_results.txt
