# 🖥️ LegacyBridge Complete CLI System Implementation
## Part 2: Full-Featured Command-Line Interface

**Target Audience**: AI Development Agent  
**Implementation Phase**: 2 of 6  
**Estimated Duration**: 2 weeks  
**Priority**: HIGH - Power user functionality and automation

---

## 🎯 **IMPLEMENTATION OVERVIEW**

### **Critical Requirements:**
The current system has **NO CLI interface**. This is a complete implementation from scratch that will provide:

1. **13 Major Commands** with full argument parsing and validation
2. **HTTP API Server Mode** with WebSocket support and authentication
3. **DLL Management System** for VB6/VFP9 compatibility
4. **Batch Processing** with parallel execution and progress tracking
5. **Format Detection** with confidence scoring and detailed analysis
6. **Performance Benchmarking** and testing capabilities

### **Architecture Overview:**
```
legacybridge/
├── src-tauri/src/cli/
│   ├── mod.rs ← CLI module exports
│   ├── app.rs ← Main CLI application (clap structure)
│   ├── commands/
│   │   ├── convert.rs ← File conversion command
│   │   ├── batch.rs ← Batch processing command
│   │   ├── detect.rs ← Format detection command
│   │   ├── serve.rs ← HTTP API server
│   │   ├── dll.rs ← DLL management commands
│   │   ├── benchmark.rs ← Performance testing
│   │   └── mod.rs ← Command exports
│   ├── output/
│   │   ├── formatter.rs ← Output formatting (JSON, table, etc.)
│   │   ├── progress.rs ← Progress bars and indicators
│   │   └── mod.rs ← Output exports
│   └── config.rs ← CLI configuration management
└── cli/ ← Standalone CLI binary (separate from Tauri)
    ├── Cargo.toml ← CLI-specific dependencies
    └── src/
        └── main.rs ← CLI entry point
```

---

## 🚀 **SECTION 1: CLI APPLICATION STRUCTURE**

### **1.1 Main CLI Application**

**File:** `src-tauri/src/cli/app.rs`

```rust
// Complete CLI Application Implementation
use clap::{Parser, Subcommand, ValueEnum, Args};
use serde::{Serialize, Deserialize};
use std::path::PathBuf;

#[derive(Parser)]
#[command(name = "legacybridge")]
#[command(version = "2.0.0")]
#[command(about = "🚀 Ultimate Document Conversion Platform - CLI Interface")]
#[command(long_about = "
LegacyBridge CLI provides powerful command-line tools for converting legacy 
and modern document formats. Supports batch processing, format detection, 
DLL building, and enterprise features.

Examples:
  legacybridge convert document.doc --output-format md
  legacybridge batch --input-dir ./docs --output-dir ./converted --pattern \"*.doc,*.wpd\"
  legacybridge detect *.* --detailed
  legacybridge serve --port 8080 --cors
  legacybridge dll build --arch x86 --output ./dist
")]
pub struct Cli {
    #[command(subcommand)]
    pub command: Commands,
    
    /// Enable verbose output (-v for basic, -vv for detailed, -vvv for debug)
    #[arg(short, long, action = clap::ArgAction::Count)]
    pub verbose: u8,
    
    /// Output format for results
    #[arg(short = 'f', long, default_value = "table")]
    pub format: OutputFormat,
    
    /// Configuration file path
    #[arg(short, long)]
    pub config: Option<PathBuf>,
    
    /// Suppress all output except errors
    #[arg(short, long)]
    pub quiet: bool,
    
    /// Enable colored output (auto-detected by default)
    #[arg(long)]
    pub color: Option<ColorChoice>,
    
    /// Working directory for relative paths
    #[arg(long)]
    pub workdir: Option<PathBuf>,
}

#[derive(Subcommand)]
pub enum Commands {
    /// 🔄 Convert files between formats
    Convert(ConvertArgs),
    
    /// 📦 Batch convert multiple files/folders
    Batch(BatchArgs),
    
    /// 🔍 Detect and analyze file formats
    Detect(DetectArgs),
    
    /// ✅ Validate file integrity and format
    Validate(ValidateArgs),
    
    /// 📤 Extract content and metadata from files
    Extract(ExtractArgs),
    
    /// 🔗 Merge multiple files into one
    Merge(MergeArgs),
    
    /// ✂️ Split large files into smaller parts
    Split(SplitArgs),
    
    /// 🗜️ Compress and optimize files
    Compress(CompressArgs),
    
    /// 🌐 Start HTTP API server
    Serve(ServeArgs),
    
    /// 🔧 DLL management and building
    Dll {
        #[command(subcommand)]
        action: DllCommands,
    },
    
    /// ⚙️ Configuration management
    Config {
        #[command(subcommand)]
        action: ConfigCommands,
    },
    
    /// 🧪 Test conversion capabilities
    Test(TestArgs),
    
    /// 📊 Performance benchmarking
    Benchmark(BenchmarkArgs),
    
    /// 🔄 Workflow automation
    Workflow {
        #[command(subcommand)]
        action: WorkflowCommands,
    },
    
    /// 🔍 Search and filter operations
    Search(SearchArgs),
    
    /// 📋 Generate reports and statistics
    Report(ReportArgs),
    
    /// ❓ Interactive help and tutorials
    Help(HelpArgs),
}

// ========================================
// CONVERT COMMAND - Most Important
// ========================================
#[derive(Args)]
pub struct ConvertArgs {
    /// Input file(s) or glob pattern
    #[arg(required = true)]
    pub input: Vec<String>,
    
    /// Output format (md, rtf, html, pdf, txt, csv, json, etc.)
    #[arg(short = 't', long)]
    pub output_format: String,
    
    /// Output directory or specific file path
    #[arg(short = 'o', long)]
    pub output: Option<PathBuf>,
    
    /// Preserve original formatting where possible
    #[arg(long)]
    pub preserve_formatting: bool,
    
    /// Enable parallel processing
    #[arg(short, long)]
    pub parallel: bool,
    
    /// Number of parallel jobs (default: CPU cores)
    #[arg(short, long)]
    pub jobs: Option<usize>,
    
    /// Conversion template/preset
    #[arg(short = 'T', long)]
    pub template: Option<String>,
    
    /// Force overwrite existing files
    #[arg(long)]
    pub force: bool,
    
    /// Preview mode - show what would be converted without doing it
    #[arg(long)]
    pub preview: bool,
    
    /// Quality level for lossy conversions (1-10)
    #[arg(long, value_parser = clap::value_parser!(u8).range(1..=10))]
    pub quality: Option<u8>,
    
    /// Custom conversion options (key=value pairs)
    #[arg(long)]
    pub options: Vec<String>,
    
    /// Continue processing on errors
    #[arg(long)]
    pub continue_on_error: bool,
}

// ========================================
// BATCH COMMAND - High Priority
// ========================================
#[derive(Args)]
pub struct BatchArgs {
    /// Input directory
    #[arg(short, long)]
    pub input_dir: PathBuf,
    
    /// Output directory
    #[arg(short, long)]
    pub output_dir: PathBuf,
    
    /// File pattern filter (e.g., "*.doc,*.wpd,*.rtf")
    #[arg(short, long, default_value = "*")]
    pub pattern: String,
    
    /// Output format for all files
    #[arg(short = 't', long)]
    pub output_format: String,
    
    /// Recursive processing of subdirectories
    #[arg(short, long)]
    pub recursive: bool,
    
    /// Maximum parallel jobs
    #[arg(long, default_value = "4")]
    pub max_jobs: usize,
    
    /// Continue processing on errors
    #[arg(long)]
    pub continue_on_error: bool,
    
    /// Generate processing report
    #[arg(long)]
    pub report: Option<PathBuf>,
    
    /// Preserve directory structure in output
    #[arg(long)]
    pub preserve_structure: bool,
    
    /// Exclude patterns (e.g., "temp*,*backup*")
    #[arg(long)]
    pub exclude: Option<String>,
    
    /// Minimum file size to process (e.g., "1KB", "1MB")
    #[arg(long)]
    pub min_size: Option<String>,
    
    /// Maximum file size to process
    #[arg(long)]
    pub max_size: Option<String>,
    
    /// Show progress for each file
    #[arg(long)]
    pub show_progress: bool,
}

// ========================================
// SERVE COMMAND - API Server
// ========================================
#[derive(Args)]
pub struct ServeArgs {
    /// Server port
    #[arg(short, long, default_value = "8080")]
    pub port: u16,
    
    /// Bind address
    #[arg(long, default_value = "127.0.0.1")]
    pub host: String,
    
    /// Enable CORS for web browsers
    #[arg(long)]
    pub cors: bool,
    
    /// API key for authentication
    #[arg(long)]
    pub api_key: Option<String>,
    
    /// Enable WebSocket support
    #[arg(long)]
    pub websocket: bool,
    
    /// Maximum request size (e.g., "10MB")
    #[arg(long, default_value = "10MB")]
    pub max_request_size: String,
    
    /// Rate limiting (requests per minute)
    #[arg(long)]
    pub rate_limit: Option<u32>,
    
    /// Enable file upload endpoint
    #[arg(long)]
    pub enable_upload: bool,
    
    /// Serve static files from directory
    #[arg(long)]
    pub static_dir: Option<PathBuf>,
    
    /// SSL certificate file
    #[arg(long)]
    pub ssl_cert: Option<PathBuf>,
    
    /// SSL private key file
    #[arg(long)]
    pub ssl_key: Option<PathBuf>,
    
    /// Enable API documentation endpoint
    #[arg(long, default_value = "true")]
    pub docs: bool,
    
    /// Enable metrics endpoint
    #[arg(long)]
    pub metrics: bool,
}

// ========================================
// DLL COMMANDS - VB6/VFP9 Support
// ========================================
#[derive(Subcommand)]
pub enum DllCommands {
    /// Build DLL for legacy systems
    Build {
        /// Target architecture (x86, x64, both)
        #[arg(long, default_value = "x86")]
        arch: Architecture,
        
        /// Output directory
        #[arg(short, long, default_value = "./dist")]
        output: PathBuf,
        
        /// Optimization level (debug, release, size)
        #[arg(long, default_value = "release")]
        optimization: OptimizationLevel,
        
        /// Include debug symbols
        #[arg(long)]
        debug: bool,
        
        /// Static linking
        #[arg(long)]
        static_linking: bool,
        
        /// Included formats (comma-separated)
        #[arg(long)]
        formats: Option<String>,
        
        /// Build configuration file
        #[arg(long)]
        config: Option<PathBuf>,
        
        /// Generate VB6 integration code
        #[arg(long)]
        generate_vb6: bool,
        
        /// Generate VFP9 integration code
        #[arg(long)]
        generate_vfp9: bool,
    },
    
    /// Test DLL compatibility
    Test {
        /// DLL file path
        dll_path: PathBuf,
        
        /// Test platforms (vb6, vfp9, generic)
        #[arg(long, default_values = ["vb6", "vfp9"])]
        platforms: Vec<Platform>,
        
        /// Test data directory
        #[arg(long)]
        test_data: Option<PathBuf>,
        
        /// Generate test report
        #[arg(long)]
        report: Option<PathBuf>,
        
        /// Verbose test output
        #[arg(long)]
        verbose: bool,
        
        /// Include performance tests
        #[arg(long)]
        performance: bool,
    },
    
    /// Generate integration code
    Generate {
        /// Target language (vb6, vfp9, c, python, csharp)
        #[arg(short, long)]
        language: Language,
        
        /// DLL file path
        #[arg(short, long)]
        dll: PathBuf,
        
        /// Output file
        #[arg(short, long)]
        output: PathBuf,
        
        /// Include examples
        #[arg(long)]
        examples: bool,
        
        /// Include error handling
        #[arg(long)]
        error_handling: bool,
        
        /// Template file
        #[arg(long)]
        template: Option<PathBuf>,
        
        /// Include documentation
        #[arg(long)]
        docs: bool,
    },
    
    /// Inspect DLL exports and dependencies
    Inspect {
        /// DLL file path
        dll_path: PathBuf,
        
        /// Show exported functions
        #[arg(long)]
        exports: bool,
        
        /// Show dependencies
        #[arg(long)]
        deps: bool,
        
        /// Show version information
        #[arg(long)]
        version: bool,
        
        /// Detailed inspection
        #[arg(long)]
        detailed: bool,
        
        /// Export inspection report
        #[arg(long)]
        report: Option<PathBuf>,
    },
    
    /// Package DLL for distribution
    Package {
        /// DLL file path
        dll_path: PathBuf,
        
        /// Output package file
        #[arg(short, long)]
        output: PathBuf,
        
        /// Package format (zip, msi, nsis)
        #[arg(long, default_value = "zip")]
        format: PackageFormat,
        
        /// Include documentation
        #[arg(long)]
        docs: bool,
        
        /// Include examples
        #[arg(long)]
        examples: bool,
        
        /// Include source code
        #[arg(long)]
        source: bool,
        
        /// Package metadata file
        #[arg(long)]
        metadata: Option<PathBuf>,
        
        /// Create installer
        #[arg(long)]
        installer: bool,
    },
}

// ========================================
// SUPPORTING ENUMS AND TYPES
// ========================================
#[derive(ValueEnum, Clone, Debug, Serialize, Deserialize)]
pub enum OutputFormat {
    Table,
    Json,
    Yaml,
    Csv,
    Plain,
    Xml,
}

#[derive(ValueEnum, Clone, Debug)]
pub enum ColorChoice {
    Always,
    Never,
    Auto,
}

#[derive(ValueEnum, Clone, Debug)]
pub enum Architecture {
    X86,
    X64,
    Both,
}

#[derive(ValueEnum, Clone, Debug)]
pub enum OptimizationLevel {
    Debug,
    Release,
    Size,
}

#[derive(ValueEnum, Clone, Debug)]
pub enum Platform {
    Vb6,
    Vfp9,
    Generic,
    DotNet,
}

#[derive(ValueEnum, Clone, Debug)]
pub enum Language {
    Vb6,
    Vfp9,
    C,
    Python,
    Csharp,
    Javascript,
    Typescript,
}

#[derive(ValueEnum, Clone, Debug)]
pub enum PackageFormat {
    Zip,
    Tar,
    Msi,
    Nsis,
    Dmg,
    Appimage,
}

// Additional Args structs for other commands...
#[derive(Args)]
pub struct DetectArgs {
    /// Files or patterns to analyze
    pub files: Vec<String>,
    
    /// Show detailed analysis including confidence scores
    #[arg(long)]
    pub detailed: bool,
    
    /// Export analysis report to file
    #[arg(long)]
    pub report: Option<PathBuf>,
    
    /// Show binary file headers (hex dump)
    #[arg(long)]
    pub show_headers: bool,
    
    /// Validate file integrity
    #[arg(long)]
    pub validate: bool,
    
    /// Show metadata extraction
    #[arg(long)]
    pub metadata: bool,
    
    /// Include alternative format suggestions
    #[arg(long)]
    pub suggestions: bool,
    
    /// Output confidence threshold (0.0-1.0)
    #[arg(long, default_value = "0.5")]
    pub confidence_threshold: f64,
}

#[derive(Args)]
pub struct BenchmarkArgs {
    /// Benchmark types to run
    #[arg(default_values = ["conversion"])]
    pub benchmarks: Vec<BenchmarkType>,
    
    /// Input files or directory for benchmarking
    #[arg(short, long)]
    pub input: Option<PathBuf>,
    
    /// Number of iterations
    #[arg(short, long, default_value = "10")]
    pub iterations: usize,
    
    /// Warmup iterations
    #[arg(long, default_value = "3")]
    pub warmup: usize,
    
    /// Output report file
    #[arg(short, long)]
    pub output: Option<PathBuf>,
    
    /// Compare against baseline
    #[arg(long)]
    pub baseline: Option<PathBuf>,
    
    /// Enable memory profiling
    #[arg(long)]
    pub memory: bool,
    
    /// Enable CPU profiling
    #[arg(long)]
    pub cpu: bool,
    
    /// Include parallel processing benchmarks
    #[arg(long)]
    pub parallel: bool,
    
    /// Test different file sizes
    #[arg(long)]
    pub size_scaling: bool,
}

#[derive(ValueEnum, Clone, Debug)]
pub enum BenchmarkType {
    Conversion,
    Detection,
    Parsing,
    Memory,
    Throughput,
    Latency,
    All,
}

// ... Additional Args structs for other commands (ValidateArgs, ExtractArgs, etc.)
```

---

## 🔄 **SECTION 2: CONVERT COMMAND IMPLEMENTATION**

### **2.1 Core Conversion Logic**

**File:** `src-tauri/src/cli/commands/convert.rs`

```rust
use super::*;
use crate::conversion::{ConversionEngine, ConversionOptions, ConversionError};
use crate::formats::{FormatRegistry, FormatDetector};
use crate::cli::output::{ProgressReporter, OutputFormatter};
use indicatif::{ProgressBar, ProgressStyle, MultiProgress};
use std::sync::Arc;
use tokio::task::JoinSet;
use tokio::sync::Semaphore;
use glob::glob;
use std::time::Instant;

pub async fn handle_convert_command(
    args: ConvertArgs, 
    global_args: &Cli
) -> Result<(), CliError> {
    let start_time = Instant::now();
    
    // Initialize services
    let format_registry = Arc::new(FormatRegistry::new());
    let detector = Arc::new(FormatDetector::new(format_registry.clone()));
    let conversion_engine = Arc::new(ConversionEngine::new());
    let formatter = OutputFormatter::new(global_args.format.clone());
    
    // Validate output format
    if !format_registry.is_supported_output(&args.output_format) {
        return Err(CliError::UnsupportedFormat {
            format: args.output_format.clone(),
            supported: format_registry.supported_outputs(),
        });
    }
    
    // Expand input patterns and collect files
    let input_files = expand_input_patterns(&args.input, global_args.workdir.as_deref())?;
    
    if input_files.is_empty() {
        return Err(CliError::NoInputFiles);
    }
    
    if global_args.verbose > 0 {
        println!("📁 Found {} files to process", input_files.len());
    }
    
    // Preview mode - show what would be processed
    if args.preview {
        return preview_conversion(&input_files, &args, &detector, &formatter).await;
    }
    
    // Setup progress reporting
    let progress = setup_progress_reporting(&input_files, global_args.quiet)?;
    let overall_progress = progress.overall_progress.clone();
    
    // Determine parallelism
    let max_parallel = calculate_parallelism(&args, input_files.len());
    
    if global_args.verbose > 0 {
        println!("⚡ Using {} parallel workers", max_parallel);
    }
    
    // Process files with parallel execution
    let results = process_files_parallel(
        input_files,
        &args,
        global_args,
        &detector,
        &conversion_engine,
        &progress,
        max_parallel,
    ).await?;
    
    // Process results and generate summary
    let summary = generate_conversion_summary(&results, start_time.elapsed());
    
    // Output results
    output_conversion_results(&summary, &formatter, global_args)?;
    
    // Final progress update
    overall_progress.finish_with_message("✅ Conversion completed");
    
    // Exit with error code if any conversions failed
    if summary.failed > 0 && !args.continue_on_error {
        std::process::exit(1);
    }
    
    Ok(())
}

async fn process_files_parallel(
    input_files: Vec<PathBuf>,
    args: &ConvertArgs,
    global_args: &Cli,
    detector: &Arc<FormatDetector>,
    conversion_engine: &Arc<ConversionEngine>,
    progress: &ProgressReporter,
    max_parallel: usize,
) -> Result<Vec<ConversionResult>, CliError> {
    let semaphore = Arc::new(Semaphore::new(max_parallel));
    let mut join_set = JoinSet::new();
    
    // Spawn tasks for each file
    for (index, input_file) in input_files.into_iter().enumerate() {
        let permit = semaphore.clone().acquire_owned().await.unwrap();
        let args = args.clone();
        let global_args = global_args.clone();
        let detector = detector.clone();
        let conversion_engine = conversion_engine.clone();
        let file_progress = progress.create_file_progress(&input_file);
        
        join_set.spawn(async move {
            let _permit = permit; // Hold permit for duration of task
            
            let result = process_single_file(
                input_file,
                &args,
                &global_args,
                &detector,
                &conversion_engine,
                &file_progress,
            ).await;
            
            file_progress.finish();
            progress.overall_progress.inc(1);
            
            (index, result)
        });
    }
    
    // Collect results maintaining order
    let mut results = vec![None; join_set.len()];
    while let Some(task_result) = join_set.join_next().await {
        let (index, result) = task_result.unwrap();
        results[index] = Some(result);
    }
    
    Ok(results.into_iter().map(|r| r.unwrap()).collect())
}

async fn process_single_file(
    input_file: PathBuf,
    args: &ConvertArgs,
    global_args: &Cli,
    detector: &FormatDetector,
    conversion_engine: &ConversionEngine,
    progress: &ProgressBar,
) -> ConversionResult {
    let start_time = Instant::now();
    
    // Update progress
    progress.set_message(format!("Processing {}", input_file.display()));
    progress.set_position(0);
    
    // Read file content
    let content = match tokio::fs::read(&input_file).await {
        Ok(content) => {
            progress.set_position(20);
            content
        }
        Err(e) => {
            return ConversionResult::error(
                input_file,
                ConversionError::FileRead(e.to_string()),
                start_time.elapsed(),
            );
        }
    };
    
    // Detect format
    let detection = match detector.detect_format_from_bytes(&content) {
        Ok(detection) => {
            progress.set_position(40);
            detection
        }
        Err(e) => {
            return ConversionResult::error(
                input_file,
                ConversionError::FormatDetection(e.to_string()),
                start_time.elapsed(),
            );
        }
    };
    
    if global_args.verbose > 1 {
        println!(
            "🔍 Detected {} format with {:.1}% confidence",
            detection.format.name,
            detection.confidence * 100.0
        );
    }
    
    // Determine output path
    let output_path = match determine_output_path(&input_file, args) {
        Ok(path) => {
            progress.set_position(50);
            path
        }
        Err(e) => {
            return ConversionResult::error(
                input_file,
                e,
                start_time.elapsed(),
            );
        }
    };
    
    // Check if output exists and handle accordingly
    if output_path.exists() && !args.force {
        return ConversionResult::error(
            input_file,
            ConversionError::OutputExists(output_path),
            start_time.elapsed(),
        );
    }
    
    // Setup conversion options
    let conversion_options = ConversionOptions {
        preserve_formatting: args.preserve_formatting,
        quality: args.quality,
        template: args.template.clone(),
        custom_options: parse_custom_options(&args.options)?,
        ..Default::default()
    };
    
    progress.set_position(60);
    progress.set_message(format!("Converting {} -> {}", 
        detection.format.name, 
        args.output_format
    ));
    
    // Perform conversion
    let converted_content = match conversion_engine.convert(
        &content,
        &detection.format.id,
        &args.output_format,
        &conversion_options
    ).await {
        Ok(content) => {
            progress.set_position(80);
            content
        }
        Err(e) => {
            return ConversionResult::error(
                input_file,
                e,
                start_time.elapsed(),
            );
        }
    };
    
    // Create output directory if needed
    if let Some(parent) = output_path.parent() {
        if let Err(e) = tokio::fs::create_dir_all(parent).await {
            return ConversionResult::error(
                input_file,
                ConversionError::DirectoryCreate(parent.to_path_buf(), e.to_string()),
                start_time.elapsed(),
            );
        }
    }
    
    // Write output file
    if let Err(e) = tokio::fs::write(&output_path, &converted_content).await {
        return ConversionResult::error(
            input_file,
            ConversionError::FileWrite(output_path, e.to_string()),
            start_time.elapsed(),
        );
    }
    
    progress.set_position(100);
    
    ConversionResult::success(
        input_file,
        output_path,
        detection.format.name,
        args.output_format.clone(),
        content.len(),
        converted_content.len(),
        start_time.elapsed(),
        detection.confidence,
    )
}

// Helper functions
fn expand_input_patterns(
    patterns: &[String], 
    workdir: Option<&Path>
) -> Result<Vec<PathBuf>, CliError> {
    let mut files = Vec::new();
    
    for pattern in patterns {
        // Handle glob patterns
        if pattern.contains('*') || pattern.contains('?') {
            let glob_pattern = if let Some(workdir) = workdir {
                workdir.join(pattern)
            } else {
                PathBuf::from(pattern)
            };
            
            for entry in glob(&glob_pattern.to_string_lossy())? {
                match entry {
                    Ok(path) => {
                        if path.is_file() {
                            files.push(path);
                        }
                    }
                    Err(e) => {
                        eprintln!("⚠️ Warning: Failed to process glob entry: {}", e);
                    }
                }
            }
        } else {
            // Handle direct file paths
            let path = if let Some(workdir) = workdir {
                workdir.join(pattern)
            } else {
                PathBuf::from(pattern)
            };
            
            if path.exists() {
                if path.is_file() {
                    files.push(path);
                } else if path.is_dir() {
                    return Err(CliError::IsDirectory(path));
                }
            } else {
                return Err(CliError::FileNotFound(path));
            }
        }
    }
    
    // Remove duplicates and sort
    files.sort();
    files.dedup();
    
    Ok(files)
}

fn calculate_parallelism(args: &ConvertArgs, file_count: usize) -> usize {
    if args.parallel {
        args.jobs.unwrap_or_else(|| {
            let cpu_count = num_cpus::get();
            std::cmp::min(cpu_count, file_count).max(1)
        })
    } else {
        1
    }
}

fn determine_output_path(
    input_file: &Path, 
    args: &ConvertArgs
) -> Result<PathBuf, ConversionError> {
    match &args.output {
        Some(output) => {
            if output.is_dir() {
                // Output directory specified - generate filename
                let stem = input_file.file_stem()
                    .ok_or_else(|| ConversionError::InvalidPath(input_file.to_path_buf()))?;
                let extension = get_extension_for_format(&args.output_format);
                let filename = format!("{}.{}", stem.to_string_lossy(), extension);
                Ok(output.join(filename))
            } else {
                // Specific output file specified
                Ok(output.clone())
            }
        }
        None => {
            // Generate output filename in same directory
            let stem = input_file.file_stem()
                .ok_or_else(|| ConversionError::InvalidPath(input_file.to_path_buf()))?;
            let extension = get_extension_for_format(&args.output_format);
            let filename = format!("{}.{}", stem.to_string_lossy(), extension);
            
            let parent = input_file.parent()
                .ok_or_else(|| ConversionError::InvalidPath(input_file.to_path_buf()))?;
            
            Ok(parent.join(filename))
        }
    }
}

fn get_extension_for_format(format: &str) -> &str {
    match format.to_lowercase().as_str() {
        "md" | "markdown" => "md",
        "rtf" => "rtf",
        "html" | "htm" => "html",
        "pdf" => "pdf",
        "txt" | "text" => "txt",
        "csv" => "csv",
        "json" => "json",
        "xml" => "xml",
        "yaml" | "yml" => "yaml",
        _ => "out", // Default extension
    }
}

// Result types
#[derive(Debug, Clone)]
pub struct ConversionResult {
    pub input_file: PathBuf,
    pub output_file: Option<PathBuf>,
    pub detected_format: String,
    pub output_format: String,
    pub file_size_input: usize,
    pub file_size_output: usize,
    pub duration: std::time::Duration,
    pub confidence: f64,
    pub success: bool,
    pub error: Option<ConversionError>,
}

impl ConversionResult {
    pub fn success(
        input_file: PathBuf,
        output_file: PathBuf,
        detected_format: String,
        output_format: String,
        file_size_input: usize,
        file_size_output: usize,
        duration: std::time::Duration,
        confidence: f64,
    ) -> Self {
        Self {
            input_file,
            output_file: Some(output_file),
            detected_format,
            output_format,
            file_size_input,
            file_size_output,
            duration,
            confidence,
            success: true,
            error: None,
        }
    }
    
    pub fn error(
        input_file: PathBuf,
        error: ConversionError,
        duration: std::time::Duration,
    ) -> Self {
        Self {
            input_file,
            output_file: None,
            detected_format: "unknown".to_string(),
            output_format: "unknown".to_string(),
            file_size_input: 0,
            file_size_output: 0,
            duration,
            confidence: 0.0,
            success: false,
            error: Some(error),
        }
    }
}

#[derive(Debug)]
pub struct ConversionSummary {
    pub total: usize,
    pub successful: usize,
    pub failed: usize,
    pub total_duration: std::time::Duration,
    pub total_input_size: usize,
    pub total_output_size: usize,
    pub results: Vec<ConversionResult>,
}
```

---

## 🌐 **SECTION 3: HTTP API SERVER MODE**

### **3.1 API Server Implementation**

**File:** `src-tauri/src/cli/commands/serve.rs`

```rust
use super::*;
use axum::{
    extract::{Query, Multipart, State, Path as AxumPath},
    http::{StatusCode, HeaderMap},
    response::Json,
    routing::{get, post},
    Router,
};
use tower_cors::{CorsLayer, Any};
use tower_http::{
    limit::RequestBodyLimitLayer,
    services::ServeDir,
    trace::TraceLayer,
    auth::RequireAuthorizationLayer,
};
use tokio::net::TcpListener;
use tokio_tungstenite::{accept_async, WebSocketStream};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use std::collections::HashMap;
use uuid::Uuid;

pub async fn handle_serve_command(
    args: ServeArgs, 
    global_args: &Cli
) -> Result<(), CliError> {
    // Initialize server state
    let app_state = Arc::new(AppState::new(args.clone())?);
    
    // Build router with all endpoints
    let app = build_router(app_state.clone(), &args).await?;
    
    // Bind and serve
    let addr = format!("{}:{}", args.host, args.port);
    let listener = TcpListener::bind(&addr).await
        .map_err(|e| CliError::ServerBind(addr.clone(), e))?;
    
    print_server_info(&args, &addr);
    
    // SSL support
    if let (Some(_cert), Some(_key)) = (&args.ssl_cert, &args.ssl_key) {
        // TODO: Implement SSL/TLS support with rustls
        println!("🔒 SSL/TLS support planned for future release");
    }
    
    axum::serve(listener, app).await
        .map_err(|e| CliError::ServerError(e.to_string()))?;
    
    Ok(())
}

async fn build_router(
    state: Arc<AppState>, 
    args: &ServeArgs
) -> Result<Router, CliError> {
    let mut app = Router::new()
        // Health and info endpoints
        .route("/", get(health_check))
        .route("/health", get(health_check))
        .route("/info", get(server_info))
        
        // Core API endpoints
        .route("/api/v1/convert", post(convert_endpoint))
        .route("/api/v1/batch", post(batch_convert_endpoint))
        .route("/api/v1/detect", post(detect_format_endpoint))
        .route("/api/v1/formats", get(list_formats_endpoint))
        .route("/api/v1/status/:job_id", get(job_status_endpoint))
        
        // File operations
        .route("/api/v1/upload", post(upload_endpoint))
        .route("/api/v1/download/:file_id", get(download_endpoint))
        
        // WebSocket endpoint
        .route("/ws/conversion", get(websocket_handler))
        
        // Administrative endpoints
        .route("/api/v1/stats", get(stats_endpoint))
        .route("/api/v1/config", get(config_endpoint))
        
        .with_state(state);
    
    // Add middleware layers
    
    // CORS support
    if args.cors {
        app = app.layer(
            CorsLayer::new()
                .allow_origin(Any)
                .allow_methods(Any)
                .allow_headers(Any)
        );
    }
    
    // Request size limit
    let max_size = parse_size(&args.max_request_size)?;
    app = app.layer(RequestBodyLimitLayer::new(max_size));
    
    // Tracing/logging
    app = app.layer(TraceLayer::new_for_http());
    
    // Authentication
    if args.api_key.is_some() {
        app = app.layer(RequireAuthorizationLayer::bearer(&args.api_key.as_ref().unwrap()));
    }
    
    // Static file serving
    if let Some(static_dir) = &args.static_dir {
        app = app.nest_service("/static", ServeDir::new(static_dir));
    }
    
    // API documentation
    if args.docs {
        app = app.route("/docs", get(api_documentation));
        app = app.route("/docs/openapi.json", get(openapi_spec));
    }
    
    // Metrics endpoint
    if args.metrics {
        app = app.route("/metrics", get(metrics_endpoint));
    }
    
    Ok(app)
}

// ========================================
// API ENDPOINT IMPLEMENTATIONS
// ========================================

// Health check endpoint
async fn health_check() -> Json<HealthResponse> {
    Json(HealthResponse {
        status: "healthy".to_string(),
        timestamp: chrono::Utc::now(),
        version: env!("CARGO_PKG_VERSION").to_string(),
        uptime: std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs(),
    })
}

// Server info endpoint
async fn server_info(State(state): State<Arc<AppState>>) -> Json<ServerInfo> {
    Json(ServerInfo {
        name: "LegacyBridge API Server".to_string(),
        version: env!("CARGO_PKG_VERSION").to_string(),
        supported_formats: state.format_registry.supported_formats(),
        features: ServerFeatures {
            websocket: state.config.websocket,
            file_upload: state.config.enable_upload,
            batch_processing: true,
            format_detection: true,
            dll_building: true,
        },
        limits: ServerLimits {
            max_file_size: state.config.max_request_size,
            max_batch_size: 100,
            rate_limit: state.config.rate_limit,
        },
    })
}

// Single file conversion endpoint
async fn convert_endpoint(
    State(state): State<Arc<AppState>>,
    Query(params): Query<ConvertParams>,
    body: String,
) -> Result<Json<ConversionResponse>, ApiError> {
    // Validate API key if required
    if let Some(_api_key) = &state.config.api_key {
        // Authentication is handled by middleware
    }
    
    // Rate limiting check
    if let Some(rate_limiter) = &state.rate_limiter {
        rate_limiter.check_rate_limit().await?;
    }
    
    // Validate input
    if body.len() > state.config.max_request_size {
        return Err(ApiError::FileTooLarge);
    }
    
    // Detect format
    let detection = state.detector.detect_format_from_bytes(body.as_bytes())
        .map_err(|e| ApiError::FormatDetection(e.to_string()))?;
    
    // Validate output format
    if !state.format_registry.is_supported_output(&params.output_format) {
        return Err(ApiError::UnsupportedOutputFormat(params.output_format));
    }
    
    // Setup conversion options
    let options = ConversionOptions {
        preserve_formatting: params.preserve_formatting.unwrap_or(false),
        quality: params.quality,
        template: params.template,
        ..Default::default()
    };
    
    // Perform conversion
    let converted_content = state.conversion_engine.convert(
        body.as_bytes(),
        &detection.format.id,
        &params.output_format,
        &options
    ).await.map_err(|e| ApiError::ConversionFailed(e.to_string()))?;
    
    // Update statistics
    state.update_stats(1, body.len(), converted_content.len()).await;
    
    Ok(Json(ConversionResponse {
        success: true,
        content: String::from_utf8_lossy(&converted_content).to_string(),
        metadata: ConversionMetadata {
            input_format: detection.format.name,
            output_format: params.output_format,
            input_size: body.len(),
            output_size: converted_content.len(),
            confidence: detection.confidence,
            processing_time_ms: 0, // TODO: Add timing
        },
        error: None,
    }))
}

// Batch conversion endpoint
async fn batch_convert_endpoint(
    State(state): State<Arc<AppState>>,
    mut multipart: Multipart,
) -> Result<Json<BatchResponse>, ApiError> {
    let job_id = Uuid::new_v4().to_string();
    let mut files = Vec::new();
    let mut options = None;
    
    // Parse multipart form data
    while let Some(field) = multipart.next_field().await.unwrap() {
        let name = field.name().unwrap_or("").to_string();
        
        match name.as_str() {
            "files" => {
                let filename = field.file_name().unwrap_or("unknown").to_string();
                let content = field.bytes().await.unwrap();
                files.push(UploadedFile {
                    filename,
                    content: content.to_vec(),
                });
            }
            "options" => {
                let options_json = field.text().await.unwrap();
                options = Some(serde_json::from_str::<BatchOptions>(&options_json)?);
            }
            _ => {
                // Ignore unknown fields
            }
        }
    }
    
    if files.is_empty() {
        return Err(ApiError::NoFilesProvided);
    }
    
    let batch_options = options.unwrap_or_default();
    
    // Store job for status tracking
    state.store_batch_job(&job_id, &files, &batch_options).await;
    
    // Process files asynchronously
    let state_clone = state.clone();
    let job_id_clone = job_id.clone();
    tokio::spawn(async move {
        process_batch_job(state_clone, job_id_clone, files, batch_options).await;
    });
    
    Ok(Json(BatchResponse {
        job_id,
        status: "processing".to_string(),
        message: format!("Processing {} files", files.len()),
        files_processed: 0,
        total_files: files.len(),
        results: vec![],
    }))
}

// WebSocket handler for real-time updates
async fn websocket_handler(
    ws: axum::extract::WebSocketUpgrade,
    State(state): State<Arc<AppState>>,
) -> axum::response::Response {
    ws.on_upgrade(|socket| handle_websocket(socket, state))
}

async fn handle_websocket(
    socket: axum::extract::ws::WebSocket,
    state: Arc<AppState>,
) {
    // WebSocket connection handler
    // Implement real-time conversion progress updates
    println!("🔌 WebSocket connection established");
    
    // TODO: Implement WebSocket message handling for real-time updates
}

// ========================================
// SUPPORTING TYPES AND STRUCTS
// ========================================

#[derive(Clone)]
pub struct AppState {
    pub config: ServeArgs,
    pub conversion_engine: Arc<ConversionEngine>,
    pub format_registry: Arc<FormatRegistry>,
    pub detector: Arc<FormatDetector>,
    pub rate_limiter: Option<Arc<RateLimiter>>,
    pub stats: Arc<tokio::sync::RwLock<ServerStats>>,
    pub batch_jobs: Arc<tokio::sync::RwLock<HashMap<String, BatchJob>>>,
}

impl AppState {
    pub fn new(config: ServeArgs) -> Result<Self, CliError> {
        let format_registry = Arc::new(FormatRegistry::new());
        let detector = Arc::new(FormatDetector::new(format_registry.clone()));
        let conversion_engine = Arc::new(ConversionEngine::new());
        
        let rate_limiter = config.rate_limit.map(|limit| {
            Arc::new(RateLimiter::new(limit))
        });
        
        Ok(Self {
            config,
            conversion_engine,
            format_registry,
            detector,
            rate_limiter,
            stats: Arc::new(tokio::sync::RwLock::new(ServerStats::default())),
            batch_jobs: Arc::new(tokio::sync::RwLock::new(HashMap::new())),
        })
    }
    
    pub async fn update_stats(&self, conversions: u64, input_bytes: usize, output_bytes: usize) {
        let mut stats = self.stats.write().await;
        stats.total_conversions += conversions;
        stats.total_input_bytes += input_bytes;
        stats.total_output_bytes += output_bytes;
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ConvertParams {
    pub output_format: String,
    pub preserve_formatting: Option<bool>,
    pub quality: Option<u8>,
    pub template: Option<String>,
}

#[derive(Debug, Serialize)]
pub struct ConversionResponse {
    pub success: bool,
    pub content: String,
    pub metadata: ConversionMetadata,
    pub error: Option<String>,
}

#[derive(Debug, Serialize)]
pub struct ConversionMetadata {
    pub input_format: String,
    pub output_format: String,
    pub input_size: usize,
    pub output_size: usize,
    pub confidence: f64,
    pub processing_time_ms: u64,
}

#[derive(Debug, Serialize)]
pub struct HealthResponse {
    pub status: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub version: String,
    pub uptime: u64,
}

#[derive(Debug, Serialize)]
pub struct ServerInfo {
    pub name: String,
    pub version: String,
    pub supported_formats: Vec<String>,
    pub features: ServerFeatures,
    pub limits: ServerLimits,
}

#[derive(Debug, Serialize)]
pub struct ServerFeatures {
    pub websocket: bool,
    pub file_upload: bool,
    pub batch_processing: bool,
    pub format_detection: bool,
    pub dll_building: bool,
}

#[derive(Debug, Serialize)]
pub struct ServerLimits {
    pub max_file_size: usize,
    pub max_batch_size: usize,
    pub rate_limit: Option<u32>,
}

// API Error handling
#[derive(Debug)]
pub enum ApiError {
    FileTooLarge,
    UnsupportedOutputFormat(String),
    FormatDetection(String),
    ConversionFailed(String),
    NoFilesProvided,
    RateLimitExceeded,
    AuthenticationRequired,
    InternalError(String),
}

impl axum::response::IntoResponse for ApiError {
    fn into_response(self) -> axum::response::Response {
        let (status, error_message) = match self {
            ApiError::FileTooLarge => (StatusCode::PAYLOAD_TOO_LARGE, "File too large"),
            ApiError::UnsupportedOutputFormat(_) => (StatusCode::BAD_REQUEST, "Unsupported output format"),
            ApiError::FormatDetection(_) => (StatusCode::BAD_REQUEST, "Format detection failed"),
            ApiError::ConversionFailed(_) => (StatusCode::INTERNAL_SERVER_ERROR, "Conversion failed"),
            ApiError::NoFilesProvided => (StatusCode::BAD_REQUEST, "No files provided"),
            ApiError::RateLimitExceeded => (StatusCode::TOO_MANY_REQUESTS, "Rate limit exceeded"),
            ApiError::AuthenticationRequired => (StatusCode::UNAUTHORIZED, "Authentication required"),
            ApiError::InternalError(_) => (StatusCode::INTERNAL_SERVER_ERROR, "Internal server error"),
        };
        
        let body = Json(serde_json::json!({
            "error": error_message,
            "details": format!("{:?}", self)
        }));
        
        (status, body).into_response()
    }
}

fn print_server_info(args: &ServeArgs, addr: &str) {
    println!("🌐 LegacyBridge API Server starting");
    println!("📍 Server URL: http://{}", addr);
    println!("📝 API Documentation: http://{}/docs", addr);
    
    if args.cors {
        println!("🌍 CORS enabled for all origins");
    }
    
    if args.api_key.is_some() {
        println!("🔐 API key authentication enabled");
    }
    
    if args.websocket {
        println!("🔌 WebSocket support enabled: ws://{}/ws/conversion", addr);
    }
    
    if args.enable_upload {
        println!("📤 File upload endpoint enabled: POST /api/v1/upload");
    }
    
    if let Some(static_dir) = &args.static_dir {
        println!("📁 Static files served from: {}", static_dir.display());
    }
    
    println!("🚀 Server ready - accepting connections");
}
```

---

## 🔧 **SECTION 4: DLL MANAGEMENT COMMANDS**

### **4.1 DLL Build Command**

**File:** `src-tauri/src/cli/commands/dll.rs`

```rust
use super::*;
use crate::dll::{DllBuilder, DllConfig, DllTestRunner, DllPackager};
use std::process::Command;

pub async fn handle_dll_command(
    action: DllCommands,
    global_args: &Cli,
) -> Result<(), CliError> {
    match action {
        DllCommands::Build { 
            arch, 
            output, 
            optimization, 
            debug, 
            static_linking, 
            formats, 
            config, 
            generate_vb6, 
            generate_vfp9 
        } => {
            handle_dll_build(
                arch, output, optimization, debug, static_linking, 
                formats, config, generate_vb6, generate_vfp9, global_args
            ).await
        }
        DllCommands::Test { 
            dll_path, 
            platforms, 
            test_data, 
            report, 
            verbose, 
            performance 
        } => {
            handle_dll_test(
                dll_path, platforms, test_data, report, verbose, performance, global_args
            ).await
        }
        DllCommands::Generate { 
            language, 
            dll, 
            output, 
            examples, 
            error_handling, 
            template, 
            docs 
        } => {
            handle_dll_generate(
                language, dll, output, examples, error_handling, template, docs, global_args
            ).await
        }
        DllCommands::Inspect { 
            dll_path, 
            exports, 
            deps, 
            version, 
            detailed, 
            report 
        } => {
            handle_dll_inspect(
                dll_path, exports, deps, version, detailed, report, global_args
            ).await
        }
        DllCommands::Package { 
            dll_path, 
            output, 
            format, 
            docs, 
            examples, 
            source, 
            metadata, 
            installer 
        } => {
            handle_dll_package(
                dll_path, output, format, docs, examples, source, metadata, installer, global_args
            ).await
        }
    }
}

async fn handle_dll_build(
    arch: Architecture,
    output: PathBuf,
    optimization: OptimizationLevel,
    debug: bool,
    static_linking: bool,
    formats: Option<String>,
    config_file: Option<PathBuf>,
    generate_vb6: bool,
    generate_vfp9: bool,
    global_args: &Cli,
) -> Result<(), CliError> {
    println!("🔧 Building LegacyBridge DLL...");
    
    // Create build configuration
    let config = DllConfig {
        architectures: match arch {
            Architecture::X86 => vec![DllArchitecture::X86],
            Architecture::X64 => vec![DllArchitecture::X64],
            Architecture::Both => vec![DllArchitecture::X86, DllArchitecture::X64],
        },
        optimization_level: match optimization {
            OptimizationLevel::Debug => DllOptimization::Debug,
            OptimizationLevel::Release => DllOptimization::Release,
            OptimizationLevel::Size => DllOptimization::Size,
        },
        include_debug_symbols: debug,
        static_linking,
        included_formats: parse_formats_list(formats),
        output_directory: output.clone(),
        generate_integration_code: GenerateIntegration {
            vb6: generate_vb6,
            vfp9: generate_vfp9,
        },
        ..Default::default()
    };
    
    // Initialize DLL builder
    let builder = DllBuilder::new(config);
    
    // Setup progress reporting
    let progress = setup_build_progress(global_args.quiet)?;
    
    // Execute build process
    let build_result = builder.build_async(|stage, progress_pct| {
        progress.set_message(format!("Building: {}", stage));
        progress.set_position(progress_pct);
    }).await?;
    
    // Report results
    report_build_results(&build_result, global_args)?;
    
    // Generate integration code if requested
    if generate_vb6 || generate_vfp9 {
        generate_integration_code(&build_result, generate_vb6, generate_vfp9, &output, global_args)?;
    }
    
    progress.finish_with_message("✅ DLL build completed successfully");
    
    Ok(())
}

async fn handle_dll_test(
    dll_path: PathBuf,
    platforms: Vec<Platform>,
    test_data: Option<PathBuf>,
    report: Option<PathBuf>,
    verbose: bool,
    performance: bool,
    global_args: &Cli,
) -> Result<(), CliError> {
    println!("🧪 Testing DLL compatibility...");
    
    // Verify DLL exists
    if !dll_path.exists() {
        return Err(CliError::FileNotFound(dll_path));
    }
    
    // Initialize test runner
    let test_runner = DllTestRunner::new(dll_path.clone());
    
    // Setup test configuration
    let test_config = DllTestConfig {
        platforms: platforms.into_iter().map(|p| match p {
            Platform::Vb6 => TestPlatform::VB6,
            Platform::Vfp9 => TestPlatform::VFP9,
            Platform::Generic => TestPlatform::Generic,
            Platform::DotNet => TestPlatform::DotNet,
        }).collect(),
        test_data_directory: test_data,
        include_performance_tests: performance,
        verbose_output: verbose || global_args.verbose > 0,
    };
    
    // Run tests
    let test_results = test_runner.run_tests(test_config).await?;
    
    // Output results
    output_test_results(&test_results, global_args)?;
    
    // Generate report if requested
    if let Some(report_path) = report {
        generate_test_report(&test_results, &report_path)?;
        println!("📋 Test report saved to: {}", report_path.display());
    }
    
    // Exit with error code if any tests failed
    if test_results.has_failures() {
        std::process::exit(1);
    }
    
    Ok(())
}

async fn handle_dll_generate(
    language: Language,
    dll_path: PathBuf,
    output: PathBuf,
    examples: bool,
    error_handling: bool,
    template: Option<PathBuf>,
    docs: bool,
    global_args: &Cli,
) -> Result<(), CliError> {
    println!("📝 Generating {} integration code...", format_language(&language));
    
    // Verify DLL exists
    if !dll_path.exists() {
        return Err(CliError::FileNotFound(dll_path));
    }
    
    // Initialize code generator
    let generator = match language {
        Language::Vb6 => CodeGenerator::vb6(),
        Language::Vfp9 => CodeGenerator::vfp9(),
        Language::C => CodeGenerator::c(),
        Language::Python => CodeGenerator::python(),
        Language::Csharp => CodeGenerator::csharp(),
        Language::Javascript => CodeGenerator::javascript(),
        Language::Typescript => CodeGenerator::typescript(),
    };
    
    // Setup generation options
    let options = CodeGenerationOptions {
        include_examples: examples,
        include_error_handling: error_handling,
        include_documentation: docs,
        template_file: template,
        output_file: output.clone(),
    };
    
    // Inspect DLL to get exports
    let dll_info = DllInspector::inspect(&dll_path)?;
    
    // Generate code
    let generated_code = generator.generate(&dll_info, &options)?;
    
    // Write to output file
    tokio::fs::write(&output, generated_code).await
        .map_err(|e| CliError::FileWrite(output.clone(), e.to_string()))?;
    
    println!("✅ Integration code generated: {}", output.display());
    
    if global_args.verbose > 0 {
        println!("📊 Generated {} lines of {} code", 
            generated_code.lines().count(), 
            format_language(&language)
        );
        
        if examples {
            println!("📚 Included usage examples");
        }
        
        if error_handling {
            println!("🛡️ Included error handling");
        }
        
        if docs {
            println!("📝 Included documentation comments");
        }
    }
    
    Ok(())
}

// Helper functions and types
fn parse_formats_list(formats: Option<String>) -> Vec<String> {
    formats
        .map(|f| f.split(',').map(|s| s.trim().to_string()).collect())
        .unwrap_or_else(|| vec![
            "rtf".to_string(),
            "doc".to_string(),
            "wordperfect".to_string(),
            "lotus123".to_string(),
            "dbase".to_string(),
            "wordstar".to_string(),
        ])
}

fn format_language(language: &Language) -> &str {
    match language {
        Language::Vb6 => "Visual Basic 6",
        Language::Vfp9 => "Visual FoxPro 9",
        Language::C => "C",
        Language::Python => "Python",
        Language::Csharp => "C#",
        Language::Javascript => "JavaScript",
        Language::Typescript => "TypeScript",
    }
}

#[derive(Debug)]
pub struct DllBuildResult {
    pub success: bool,
    pub architectures_built: Vec<DllArchitecture>,
    pub output_files: Vec<PathBuf>,
    pub build_duration: std::time::Duration,
    pub warnings: Vec<String>,
    pub errors: Vec<String>,
}

#[derive(Debug)]
pub struct DllTestResults {
    pub platform_results: HashMap<TestPlatform, PlatformTestResult>,
    pub overall_success: bool,
    pub total_tests: usize,
    pub passed_tests: usize,
    pub failed_tests: usize,
    pub test_duration: std::time::Duration,
}

impl DllTestResults {
    pub fn has_failures(&self) -> bool {
        !self.overall_success
    }
}
```

---

## 📊 **SECTION 5: IMPLEMENTATION CHECKLIST**

### **Phase 1: Core CLI Structure (Week 1)**
- [ ] **Day 1-2**: Set up clap application structure with all 13 commands
- [ ] **Day 3-4**: Implement convert command with parallel processing
- [ ] **Day 5**: Add progress bars and output formatting
- [ ] **Day 6-7**: Implement batch processing command

### **Phase 2: API Server & DLL Commands (Week 2)**
- [ ] **Day 1-3**: Build HTTP API server with all endpoints
- [ ] **Day 4**: Add WebSocket support for real-time updates
- [ ] **Day 5-6**: Implement DLL build and test commands
- [ ] **Day 7**: Add format detection and validation commands

### **Required Dependencies (Cargo.toml):**
```toml
[dependencies]
clap = { version = "4.0", features = ["derive", "env"] }
tokio = { version = "1.0", features = ["full"] }
axum = { version = "0.7", features = ["multipart", "ws"] }
tower = { version = "0.4", features = ["full"] }
tower-http = { version = "0.5", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
indicatif = "0.17"
glob = "0.3"
uuid = { version = "1.0", features = ["v4"] }
chrono = { version = "0.4", features = ["serde"] }
tracing = "0.1"
tracing-subscriber = "0.3"
num_cpus = "1.0"
```

### **Testing Requirements:**
- [ ] Unit tests for all CLI commands
- [ ] Integration tests for API endpoints
- [ ] Performance tests for batch processing
- [ ] Compatibility tests for DLL functions
- [ ] End-to-end tests with sample files

### **Documentation Requirements:**
- [ ] CLI help text for all commands
- [ ] API documentation with OpenAPI spec
- [ ] Integration examples for VB6/VFP9
- [ ] Performance benchmarking guides
- [ ] Troubleshooting documentation

---

## 🎯 **SUCCESS METRICS**

### **Performance Targets:**
- **CLI Startup**: < 100ms for help/simple commands
- **Conversion Speed**: > 1000 files/minute for small files
- **API Response**: < 200ms for single file conversion
- **Memory Usage**: < 100MB for CLI operations
- **Throughput**: > 10MB/second conversion rate

### **Usability Targets:**
- **Command Discovery**: Intuitive command names and help
- **Error Messages**: Clear, actionable error descriptions
- **Progress Reporting**: Real-time progress for long operations
- **Output Formatting**: Multiple formats (table, JSON, CSV)
- **Configuration**: Flexible config file and environment variable support

This completes the Complete CLI System implementation guide. The next document will cover the MCP Server Integration implementation.