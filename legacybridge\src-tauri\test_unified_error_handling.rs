// Test Unified Error Handling System
// Validates that the new unified error system works correctly across all interfaces

use std::ffi::{CString, CStr};
use std::os::raw::{c_char, c_int};
use std::ptr;

use legacybridge::conversion::{UnifiedError, UnifiedResult, ToUnifiedError, ToUnifiedResult};
use legacybridge::conversion::error::ConversionError;
use legacybridge::ffi_unified::*;

// External function declarations for the renamed functions
extern "C" {
    fn legacybridge_clear_last_error_unified();
    fn legacybridge_free_string_unified(ptr: *mut c_char);
    fn legacybridge_get_version_unified() -> *mut c_char;
    fn legacybridge_is_initialized_unified() -> c_int;
    fn legacybridge_get_last_error_unified() -> *mut c_char;
    fn legacybridge_get_last_error_id_unified() -> *mut c_char;
}

#[tokio::main]
async fn main() {
    println!("Unified Error Handling Test");
    println!("===========================\n");
    
    test_unified_error_creation();
    test_error_conversion();
    test_ffi_error_handling();
    test_error_tracking();
    
    println!("\n✅ All unified error handling tests completed successfully!");
}

fn test_unified_error_creation() {
    println!("1. Testing Unified Error Creation");
    println!("---------------------------------");
    
    // Test different error types
    let invalid_input = UnifiedError::invalid_input("Test invalid input");
    println!("  ✅ Invalid input error: {} [ID: {}]", invalid_input.user_message(), invalid_input.error_id());
    assert_eq!(invalid_input.to_ffi_code(), -1);
    
    let conversion_failed = UnifiedError::conversion_failed_with_formats(
        "Test conversion failure", "RTF", "Markdown"
    );
    println!("  ✅ Conversion error: {} [ID: {}]", conversion_failed.user_message(), conversion_failed.error_id());
    assert_eq!(conversion_failed.to_ffi_code(), -2);
    
    let resource_limit = UnifiedError::resource_limit(
        "Memory limit exceeded", "memory", Some(1000), Some(500)
    );
    println!("  ✅ Resource limit error: {} [ID: {}]", resource_limit.user_message(), resource_limit.error_id());
    assert_eq!(resource_limit.to_ffi_code(), -4);
    
    let security_violation = UnifiedError::security_violation(
        "Malicious content detected", "content_validation"
    );
    println!("  ✅ Security error: {} [ID: {}]", security_violation.user_message(), security_violation.error_id());
    assert_eq!(security_violation.to_ffi_code(), -5);
    
    let timeout_error = UnifiedError::timeout("Operation timed out", 30);
    println!("  ✅ Timeout error: {} [ID: {}]", timeout_error.user_message(), timeout_error.error_id());
    assert_eq!(timeout_error.to_ffi_code(), -6);
    
    let internal_error = UnifiedError::internal_error("Internal system failure");
    println!("  ✅ Internal error: {} [ID: {}]", internal_error.user_message(), internal_error.error_id());
    assert_eq!(internal_error.to_ffi_code(), -99);
    assert_eq!(internal_error.user_message(), "An internal error occurred. Please contact support.");
    
    println!("  ✅ Error creation tests passed\n");
}

fn test_error_conversion() {
    println!("2. Testing Error Conversion");
    println!("---------------------------");
    
    // Test conversion from legacy ConversionError to UnifiedError
    let legacy_error = ConversionError::InvalidInput("Legacy invalid input".to_string());
    let unified_error = legacy_error.to_unified();
    println!("  ✅ Legacy to unified conversion: {} [ID: {}]", 
             unified_error.user_message(), unified_error.error_id());
    
    // Test Result conversion
    let legacy_result: Result<String, ConversionError> = Err(ConversionError::Parser("Parse error".to_string()));
    let unified_result: UnifiedResult<String> = legacy_result.to_unified_result();
    assert!(unified_result.is_err());
    let error = unified_result.unwrap_err();
    println!("  ✅ Result conversion: {} [ID: {}]", error.user_message(), error.error_id());
    
    // Test conversion back to legacy error
    let unified = UnifiedError::conversion_failed("Test conversion");
    let legacy: ConversionError = unified.into();
    println!("  ✅ Unified to legacy conversion: {}", legacy);
    
    println!("  ✅ Error conversion tests passed\n");
}

fn test_ffi_error_handling() {
    println!("3. Testing FFI Error Handling");
    println!("-----------------------------");
    
    unsafe {
        // Test null pointer handling
        let mut output_buffer: *mut c_char = ptr::null_mut();
        let mut output_length: c_int = 0;
        
        let result = legacybridge_rtf_to_markdown_unified(
            ptr::null(),
            &mut output_buffer,
            &mut output_length,
        );
        
        assert_ne!(result, 0);
        println!("  ✅ Null pointer handling: Error code {}", result);
        
        // Get error message
        let error_msg_ptr = legacybridge_get_last_error_unified();
        if !error_msg_ptr.is_null() {
            let error_msg = CStr::from_ptr(error_msg_ptr).to_string_lossy();
            println!("  ✅ Error message: {}", error_msg);
            legacybridge_free_string_unified(error_msg_ptr);
        }

        // Get error ID
        let error_id_ptr = legacybridge_get_last_error_id_unified();
        if !error_id_ptr.is_null() {
            let error_id = CStr::from_ptr(error_id_ptr).to_string_lossy();
            println!("  ✅ Error ID: {}", error_id);
            legacybridge_free_string_unified(error_id_ptr);
        }

        // Clear error
        legacybridge_clear_last_error_unified();
        let cleared_error = legacybridge_get_last_error_unified();
        assert!(cleared_error.is_null());
        println!("  ✅ Error clearing works");
        
        // Test valid conversion
        let rtf_content = CString::new(r"{\rtf1\ansi\deff0 {\fonttbl {\f0 Times New Roman;}} \f0\fs24 Test document.}").unwrap();
        let result = legacybridge_rtf_to_markdown_unified(
            rtf_content.as_ptr(),
            &mut output_buffer,
            &mut output_length,
        );
        
        if result == 0 {
            println!("  ✅ Valid conversion succeeded");
            if !output_buffer.is_null() {
                let output = CStr::from_ptr(output_buffer).to_string_lossy();
                println!("  ✅ Output: {}", output);
                legacybridge_free_string_unified(output_buffer);
            }
        } else {
            println!("  ⚠️ Valid conversion failed with code: {}", result);
        }
        
        // Test size limit
        let large_content = "x".repeat(11 * 1024 * 1024); // 11MB > 10MB limit
        let large_rtf = CString::new(large_content).unwrap();
        let result = legacybridge_rtf_to_markdown_unified(
            large_rtf.as_ptr(),
            &mut output_buffer,
            &mut output_length,
        );
        
        assert_ne!(result, 0);
        println!("  ✅ Size limit enforcement: Error code {}", result);
        
        // Get version info
        let version_ptr = legacybridge_get_version_unified();
        if !version_ptr.is_null() {
            let version = CStr::from_ptr(version_ptr).to_string_lossy();
            println!("  ✅ Version: {}", version);
            legacybridge_free_string_unified(version_ptr);
        }

        // Test initialization check
        let is_init = legacybridge_is_initialized_unified();
        println!("  ✅ Initialization status: {}", is_init);
    }
    
    println!("  ✅ FFI error handling tests passed\n");
}

fn test_error_tracking() {
    println!("4. Testing Error Tracking");
    println!("-------------------------");
    
    // Test error ID uniqueness
    let error1 = UnifiedError::invalid_input("Error 1");
    let error2 = UnifiedError::invalid_input("Error 2");
    assert_ne!(error1.error_id(), error2.error_id());
    println!("  ✅ Error IDs are unique: {} vs {}", error1.error_id(), error2.error_id());
    
    // Test error ID format
    let error = UnifiedError::conversion_failed("Test error");
    let error_id = error.error_id();
    assert!(error_id.starts_with("ERR-"));
    assert!(error_id.len() > 10);
    println!("  ✅ Error ID format: {}", error_id);
    
    // Test user vs developer messages
    let internal_error = UnifiedError::internal_error("Sensitive internal details");
    let user_msg = internal_error.user_message();
    let dev_msg = internal_error.developer_message();
    assert_ne!(user_msg, dev_msg);
    assert!(!user_msg.contains("Sensitive"));
    assert!(dev_msg.contains("Sensitive"));
    println!("  ✅ User message: {}", user_msg);
    println!("  ✅ Developer message: {}", dev_msg);
    
    println!("  ✅ Error tracking tests passed\n");
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_error_codes() {
        assert_eq!(UnifiedError::invalid_input("test").to_ffi_code(), -1);
        assert_eq!(UnifiedError::conversion_failed("test").to_ffi_code(), -2);
        assert_eq!(UnifiedError::io_error("test", None, None).to_ffi_code(), -3);
        assert_eq!(UnifiedError::resource_limit("test", "memory", None, None).to_ffi_code(), -4);
        assert_eq!(UnifiedError::security_violation("test", "general").to_ffi_code(), -5);
        assert_eq!(UnifiedError::timeout("test", 30).to_ffi_code(), -6);
        assert_eq!(UnifiedError::system_error("test", "component").to_ffi_code(), -7);
        assert_eq!(UnifiedError::internal_error("test").to_ffi_code(), -99);
    }

    #[test]
    fn test_error_serialization() {
        let error = UnifiedError::invalid_input("Test error");
        let json = serde_json::to_string(&error).unwrap();
        let deserialized: UnifiedError = serde_json::from_str(&json).unwrap();
        assert_eq!(error.user_message(), deserialized.user_message());
    }
}
