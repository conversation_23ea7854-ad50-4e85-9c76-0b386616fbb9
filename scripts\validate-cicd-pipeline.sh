#!/bin/bash

# LegacyBridge CI/CD Pipeline Validation Script
# This script validates the CI/CD pipeline configuration and components

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Validation counters
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

# Function to run a check
run_check() {
    local check_name="$1"
    local check_command="$2"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    log_info "Running check: $check_name"
    
    if eval "$check_command"; then
        log_success "$check_name - PASSED"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        log_error "$check_name - FAILED"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
        return 1
    fi
}

# Check if required tools are installed
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    run_check "Docker installed" "command -v docker >/dev/null 2>&1"
    run_check "Node.js installed" "command -v node >/dev/null 2>&1"
    run_check "npm installed" "command -v npm >/dev/null 2>&1"
    run_check "Rust installed" "command -v cargo >/dev/null 2>&1"
    run_check "kubectl installed" "command -v kubectl >/dev/null 2>&1"
    run_check "jq installed" "command -v jq >/dev/null 2>&1"
}

# Validate GitHub Actions workflows
validate_workflows() {
    log_info "Validating GitHub Actions workflows..."
    
    local workflow_dir=".github/workflows"
    
    run_check "Workflows directory exists" "[ -d '$workflow_dir' ]"
    run_check "Main CI workflow exists" "[ -f '$workflow_dir/ci.yml' ]"
    run_check "Multi-arch build workflow exists" "[ -f '$workflow_dir/build-multiarch.yml' ]"
    run_check "Deployment workflow exists" "[ -f '$workflow_dir/deploy.yml' ]"
    run_check "Performance test workflow exists" "[ -f '$workflow_dir/performance-test.yml' ]"
    run_check "Security gates workflow exists" "[ -f '$workflow_dir/security-gates.yml' ]"
    
    # Validate workflow syntax
    for workflow in "$workflow_dir"/*.yml; do
        if [ -f "$workflow" ]; then
            local workflow_name=$(basename "$workflow")
            run_check "Workflow $workflow_name syntax" "python3 -c 'import yaml; yaml.safe_load(open(\"$workflow\"))' >/dev/null 2>&1"
        fi
    done
}

# Validate Docker configuration
validate_docker() {
    log_info "Validating Docker configuration..."
    
    run_check "Dockerfile.optimized exists" "[ -f 'Dockerfile.optimized' ]"
    run_check ".dockerignore exists" "[ -f '.dockerignore' ]"
    run_check "docker-compose.yml exists" "[ -f 'docker-compose.yml' ]"
    
    # Test Docker build (dry run)
    if command -v docker >/dev/null 2>&1; then
        run_check "Docker build test" "docker build --dry-run -f Dockerfile.optimized . >/dev/null 2>&1"
    else
        log_warning "Docker not available, skipping build test"
    fi
}

# Validate Kubernetes manifests
validate_kubernetes() {
    log_info "Validating Kubernetes manifests..."
    
    local k8s_dir="k8s"
    
    run_check "Kubernetes directory exists" "[ -d '$k8s_dir' ]"
    run_check "Deployment manifest exists" "[ -f '$k8s_dir/deployment.yaml' ]"
    run_check "RBAC manifest exists" "[ -f '$k8s_dir/rbac.yaml' ]"
    run_check "Network policy manifest exists" "[ -f '$k8s_dir/network-policy.yaml' ]"
    run_check "Ingress manifest exists" "[ -f '$k8s_dir/ingress.yaml' ]"
    
    # Validate YAML syntax
    for manifest in "$k8s_dir"/*.yaml; do
        if [ -f "$manifest" ]; then
            local manifest_name=$(basename "$manifest")
            run_check "K8s manifest $manifest_name syntax" "python3 -c 'import yaml; yaml.safe_load(open(\"$manifest\"))' >/dev/null 2>&1"
        fi
    done
    
    # Validate with kubectl if available
    if command -v kubectl >/dev/null 2>&1; then
        for manifest in "$k8s_dir"/*.yaml; do
            if [ -f "$manifest" ]; then
                local manifest_name=$(basename "$manifest")
                run_check "K8s manifest $manifest_name validation" "kubectl apply --dry-run=client -f '$manifest' >/dev/null 2>&1"
            fi
        done
    else
        log_warning "kubectl not available, skipping manifest validation"
    fi
}

# Validate application configuration
validate_application() {
    log_info "Validating application configuration..."
    
    local app_dir="legacybridge"
    
    run_check "Application directory exists" "[ -d '$app_dir' ]"
    run_check "package.json exists" "[ -f '$app_dir/package.json' ]"
    run_check "tsconfig.json exists" "[ -f '$app_dir/tsconfig.json' ]"
    run_check "Rust Cargo.toml exists" "[ -f '$app_dir/src-tauri/Cargo.toml' ]"
    
    # Validate package.json
    if [ -f "$app_dir/package.json" ]; then
        run_check "package.json syntax" "jq empty '$app_dir/package.json' >/dev/null 2>&1"
        run_check "Required npm scripts exist" "jq -e '.scripts.build and .scripts.test and .scripts.start' '$app_dir/package.json' >/dev/null 2>&1"
    fi
    
    # Check for security configurations
    run_check "Lighthouse config exists" "[ -f '.lighthouserc.json' ]"
    run_check "ESLint config exists" "[ -f '$app_dir/eslint.config.mjs' ] || [ -f '$app_dir/.eslintrc.js' ]"
}

# Test build process
test_build() {
    log_info "Testing build process..."
    
    local app_dir="legacybridge"
    
    if [ -d "$app_dir" ]; then
        cd "$app_dir"
        
        # Test npm install
        if command -v npm >/dev/null 2>&1; then
            run_check "npm install" "npm ci --silent >/dev/null 2>&1"
            
            # Test TypeScript compilation
            if [ -f "tsconfig.json" ]; then
                run_check "TypeScript compilation" "npx tsc --noEmit >/dev/null 2>&1"
            fi
            
            # Test linting
            run_check "ESLint check" "npm run lint >/dev/null 2>&1 || true"
        else
            log_warning "npm not available, skipping npm tests"
        fi
        
        # Test Rust build
        if [ -d "src-tauri" ] && command -v cargo >/dev/null 2>&1; then
            cd "src-tauri"
            run_check "Rust compilation check" "cargo check >/dev/null 2>&1"
            run_check "Rust clippy check" "cargo clippy --all-targets --all-features -- -D warnings >/dev/null 2>&1"
            cd ..
        else
            log_warning "Rust not available or src-tauri not found, skipping Rust tests"
        fi
        
        cd ..
    else
        log_warning "Application directory not found, skipping build tests"
    fi
}

# Validate security configurations
validate_security() {
    log_info "Validating security configurations..."
    
    run_check "Security workflow exists" "[ -f '.github/workflows/security-gates.yml' ]"
    run_check "Dependabot config exists" "[ -f '.github/dependabot.yml' ] || [ -f '.dependabot/config.yml' ]"
    
    # Check for security-related files
    run_check "Security policy exists" "[ -f 'SECURITY.md' ] || [ -f '.github/SECURITY.md' ]"
    
    # Check Docker security
    if [ -f "Dockerfile.optimized" ]; then
        run_check "Docker runs as non-root" "grep -q 'USER.*[^0]' Dockerfile.optimized"
        run_check "Docker has health check" "grep -q 'HEALTHCHECK' Dockerfile.optimized"
    fi
}

# Generate validation report
generate_report() {
    log_info "Generating validation report..."
    
    local report_file="cicd-validation-report.md"
    
    cat > "$report_file" << EOF
# CI/CD Pipeline Validation Report

**Generated**: $(date -u)
**Repository**: $(git remote get-url origin 2>/dev/null || echo "Unknown")
**Branch**: $(git branch --show-current 2>/dev/null || echo "Unknown")
**Commit**: $(git rev-parse HEAD 2>/dev/null || echo "Unknown")

## Summary

- **Total Checks**: $TOTAL_CHECKS
- **Passed**: $PASSED_CHECKS
- **Failed**: $FAILED_CHECKS
- **Success Rate**: $(( PASSED_CHECKS * 100 / TOTAL_CHECKS ))%

## Status

EOF

    if [ $FAILED_CHECKS -eq 0 ]; then
        echo "✅ **Overall Status**: ALL CHECKS PASSED" >> "$report_file"
        echo "" >> "$report_file"
        echo "The CI/CD pipeline is ready for production use." >> "$report_file"
    else
        echo "❌ **Overall Status**: $FAILED_CHECKS CHECKS FAILED" >> "$report_file"
        echo "" >> "$report_file"
        echo "Please address the failed checks before using the CI/CD pipeline." >> "$report_file"
    fi
    
    cat >> "$report_file" << EOF

## Recommendations

- Run this validation script regularly
- Keep all dependencies updated
- Monitor security vulnerabilities
- Test deployments in staging first
- Review and update configurations quarterly

## Next Steps

- [ ] Address any failed checks
- [ ] Test the pipeline with a sample deployment
- [ ] Set up monitoring and alerting
- [ ] Train team on CI/CD processes

EOF

    log_success "Validation report generated: $report_file"
}

# Main execution
main() {
    log_info "Starting LegacyBridge CI/CD Pipeline Validation"
    log_info "================================================"
    
    check_prerequisites
    validate_workflows
    validate_docker
    validate_kubernetes
    validate_application
    test_build
    validate_security
    
    log_info "================================================"
    log_info "Validation Summary:"
    log_info "Total Checks: $TOTAL_CHECKS"
    log_success "Passed: $PASSED_CHECKS"
    
    if [ $FAILED_CHECKS -gt 0 ]; then
        log_error "Failed: $FAILED_CHECKS"
    fi
    
    generate_report
    
    if [ $FAILED_CHECKS -eq 0 ]; then
        log_success "🎉 All validations passed! CI/CD pipeline is ready."
        exit 0
    else
        log_error "❌ Some validations failed. Please review and fix the issues."
        exit 1
    fi
}

# Run main function
main "$@"
