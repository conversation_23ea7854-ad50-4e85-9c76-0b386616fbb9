# Network Policies for LegacyBridge Security
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: legacybridge-network-policy
  namespace: legacybridge
  labels:
    app: legacybridge
    app.kubernetes.io/name: legacybridge
    app.kubernetes.io/component: network-policy
spec:
  podSelector:
    matchLabels:
      app: legacybridge
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # Allow ingress from ingress controller
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    ports:
    - protocol: TCP
      port: 3030
  # Allow ingress from monitoring (Prometheus)
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    ports:
    - protocol: TCP
      port: 9090
  # Allow ingress from same namespace (for service mesh)
  - from:
    - namespaceSelector:
        matchLabels:
          name: legacybridge
    ports:
    - protocol: TCP
      port: 3030
    - protocol: TCP
      port: 9090
  # Allow health checks from kube-system
  - from:
    - namespaceSelector:
        matchLabels:
          name: kube-system
    ports:
    - protocol: TCP
      port: 3030
  egress:
  # Allow DNS resolution
  - to: []
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53
  # Allow HTTPS outbound (for external APIs)
  - to: []
    ports:
    - protocol: TCP
      port: 443
  # Allow HTTP outbound (for health checks)
  - to: []
    ports:
    - protocol: TCP
      port: 80
  # Allow communication within namespace
  - to:
    - namespaceSelector:
        matchLabels:
          name: legacybridge
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: deny-all-default
  namespace: legacybridge
  labels:
    app: legacybridge
    app.kubernetes.io/name: legacybridge
    app.kubernetes.io/component: network-policy
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-monitoring
  namespace: legacybridge
  labels:
    app: legacybridge
    app.kubernetes.io/name: legacybridge
    app.kubernetes.io/component: network-policy
spec:
  podSelector:
    matchLabels:
      app: legacybridge
  policyTypes:
  - Ingress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    - namespaceSelector:
        matchLabels:
          name: kube-system
    - podSelector:
        matchLabels:
          app: prometheus
    - podSelector:
        matchLabels:
          app: grafana
    ports:
    - protocol: TCP
      port: 9090
---
apiVersion: v1
kind: LimitRange
metadata:
  name: legacybridge-limits
  namespace: legacybridge
  labels:
    app: legacybridge
    app.kubernetes.io/name: legacybridge
    app.kubernetes.io/component: resource-limits
spec:
  limits:
  - type: Container
    default:
      cpu: "1000m"
      memory: "1Gi"
      ephemeral-storage: "2Gi"
    defaultRequest:
      cpu: "500m"
      memory: "512Mi"
      ephemeral-storage: "1Gi"
    max:
      cpu: "4000m"
      memory: "4Gi"
      ephemeral-storage: "10Gi"
    min:
      cpu: "100m"
      memory: "128Mi"
      ephemeral-storage: "500Mi"
  - type: Pod
    max:
      cpu: "8000m"
      memory: "8Gi"
      ephemeral-storage: "20Gi"
  - type: PersistentVolumeClaim
    max:
      storage: "100Gi"
    min:
      storage: "1Gi"
---
apiVersion: v1
kind: ResourceQuota
metadata:
  name: legacybridge-quota
  namespace: legacybridge
  labels:
    app: legacybridge
    app.kubernetes.io/name: legacybridge
    app.kubernetes.io/component: resource-quota
spec:
  hard:
    # Compute resources
    requests.cpu: "10"
    requests.memory: "20Gi"
    requests.ephemeral-storage: "50Gi"
    limits.cpu: "20"
    limits.memory: "40Gi"
    limits.ephemeral-storage: "100Gi"
    
    # Object counts
    pods: "50"
    services: "10"
    secrets: "20"
    configmaps: "20"
    persistentvolumeclaims: "10"
    
    # Storage
    requests.storage: "500Gi"
    
    # Load balancers
    services.loadbalancers: "2"
    services.nodeports: "5"
---
apiVersion: policy/v1beta1
kind: PodSecurityPolicy
metadata:
  name: legacybridge-psp
  labels:
    app: legacybridge
    app.kubernetes.io/name: legacybridge
    app.kubernetes.io/component: security-policy
spec:
  privileged: false
  allowPrivilegeEscalation: false
  requiredDropCapabilities:
    - ALL
  volumes:
    - 'configMap'
    - 'emptyDir'
    - 'projected'
    - 'secret'
    - 'downwardAPI'
    - 'persistentVolumeClaim'
  runAsUser:
    rule: 'MustRunAsNonRoot'
  runAsGroup:
    rule: 'MustRunAs'
    ranges:
      - min: 1001
        max: 1001
  seLinux:
    rule: 'RunAsAny'
  fsGroup:
    rule: 'MustRunAs'
    ranges:
      - min: 1001
        max: 1001
  readOnlyRootFilesystem: true
  seccompProfile:
    type: 'RuntimeDefault'
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: legacybridge-psp-user
  labels:
    app: legacybridge
    app.kubernetes.io/name: legacybridge
    app.kubernetes.io/component: security-policy
rules:
- apiGroups: ['policy']
  resources: ['podsecuritypolicies']
  verbs: ['use']
  resourceNames:
  - legacybridge-psp
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: legacybridge-psp-user
  labels:
    app: legacybridge
    app.kubernetes.io/name: legacybridge
    app.kubernetes.io/component: security-policy
roleRef:
  kind: ClusterRole
  name: legacybridge-psp-user
  apiGroup: rbac.authorization.k8s.io
subjects:
- kind: ServiceAccount
  name: legacybridge
  namespace: legacybridge
