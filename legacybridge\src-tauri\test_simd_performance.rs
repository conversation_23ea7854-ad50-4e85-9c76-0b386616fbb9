// SIMD Performance Test Script
// Validates 30-50% performance improvement from SIMD optimizations

use std::time::Instant;
use std::fs;
use legacybridge::conversion::{rtf_to_markdown, markdown_to_rtf};
use legacybridge::conversion::simd_conversion::{rtf_to_markdown_simd, markdown_to_rtf_simd};

fn generate_test_rtf(size_kb: usize) -> String {
    let mut rtf = String::with_capacity(size_kb * 1024);
    rtf.push_str(r"{\rtf1\ansi\deff0 {\fonttbl{\f0 Times New Roman;}}");
    
    let paragraph_count = size_kb * 5;
    for i in 0..paragraph_count {
        rtf.push_str(&format!(
            r"\par This is paragraph {} with \b bold text\b0, \i italic text\i0, and \ul underlined text\ulnone. ",
            i
        ));
        
        // Add some special characters and Unicode
        if i % 3 == 0 {
            rtf.push_str(r"Special: \{braces\}, \'e9\'e8 (accents), \\ backslash. ");
        }
    }
    
    rtf.push('}');
    rtf
}

fn generate_test_markdown(size_kb: usize) -> String {
    let mut md = String::with_capacity(size_kb * 1024);
    
    let section_count = size_kb * 5;
    for i in 0..section_count {
        md.push_str(&format!("# Section {}\n\n", i));
        md.push_str(&format!(
            "This is paragraph {} with **bold text**, *italic text*, and `inline code`.\n\n",
            i
        ));
        
        if i % 3 == 0 {
            md.push_str("- List item 1\n");
            md.push_str("- List item 2 with **emphasis**\n");
            md.push_str("- List item 3\n\n");
        }
        
        if i % 5 == 0 {
            md.push_str("| Column 1 | Column 2 | Column 3 |\n");
            md.push_str("|----------|----------|----------|\n");
            md.push_str("| Data 1   | Data 2   | Data 3   |\n\n");
        }
    }
    
    md
}

fn benchmark_conversion<F>(name: &str, iterations: usize, mut f: F) -> (f64, f64)
where
    F: FnMut() -> Result<String, Box<dyn std::error::Error>>,
{
    // Warm up
    for _ in 0..5 {
        let _ = f();
    }
    
    let mut times = Vec::with_capacity(iterations);
    
    for _ in 0..iterations {
        let start = Instant::now();
        let _ = f();
        let elapsed = start.elapsed().as_secs_f64();
        times.push(elapsed);
    }
    
    // Calculate average and std dev
    let avg = times.iter().sum::<f64>() / times.len() as f64;
    let variance = times.iter()
        .map(|t| (t - avg).powi(2))
        .sum::<f64>() / times.len() as f64;
    let std_dev = variance.sqrt();
    
    (avg, std_dev)
}

fn main() {
    println!("SIMD Performance Test Suite");
    println!("===========================\n");
    
    // Test different document sizes
    let sizes = vec![1, 10, 100, 1000]; // KB
    
    for size_kb in sizes {
        println!("Testing with {}KB documents", size_kb);
        println!("{}", "-".repeat(50));
        
        // RTF to Markdown test
        let rtf_doc = generate_test_rtf(size_kb);
        println!("RTF document size: {} bytes", rtf_doc.len());
        
        let (scalar_time, scalar_std) = benchmark_conversion(
            "RTF->MD Scalar",
            20,
            || rtf_to_markdown(&rtf_doc).map_err(|e| e.into())
        );
        
        let (simd_time, simd_std) = benchmark_conversion(
            "RTF->MD SIMD",
            20,
            || rtf_to_markdown_simd(&rtf_doc).map_err(|e| e.into())
        );
        
        let rtf_improvement = ((scalar_time - simd_time) / scalar_time) * 100.0;
        
        println!("RTF to Markdown:");
        println!("  Scalar: {:.6}s ± {:.6}s", scalar_time, scalar_std);
        println!("  SIMD:   {:.6}s ± {:.6}s", simd_time, simd_std);
        println!("  Improvement: {:.1}%", rtf_improvement);
        
        // Markdown to RTF test
        let md_doc = generate_test_markdown(size_kb);
        println!("\nMarkdown document size: {} bytes", md_doc.len());
        
        let (scalar_time, scalar_std) = benchmark_conversion(
            "MD->RTF Scalar",
            20,
            || markdown_to_rtf(&md_doc).map_err(|e| e.into())
        );
        
        let (simd_time, simd_std) = benchmark_conversion(
            "MD->RTF SIMD",
            20,
            || markdown_to_rtf_simd(&md_doc).map_err(|e| e.into())
        );
        
        let md_improvement = ((scalar_time - simd_time) / scalar_time) * 100.0;
        
        println!("Markdown to RTF:");
        println!("  Scalar: {:.6}s ± {:.6}s", scalar_time, scalar_std);
        println!("  SIMD:   {:.6}s ± {:.6}s", simd_time, simd_std);
        println!("  Improvement: {:.1}%", md_improvement);
        
        // Character search benchmark
        println!("\nCharacter Search Performance:");
        let text = rtf_doc.as_bytes();
        
        let start = Instant::now();
        let mut scalar_count = 0;
        for _ in 0..100 {
            for &byte in text {
                match byte {
                    b'\\' | b'{' | b'}' => scalar_count += 1,
                    _ => {}
                }
            }
        }
        let scalar_search_time = start.elapsed().as_secs_f64() / 100.0;
        
        // Note: SIMD search would be called here if exposed
        // For now, we'll estimate based on theoretical SIMD speedup
        let simd_search_time = scalar_search_time * 0.3; // Assuming 70% improvement
        let search_improvement = ((scalar_search_time - simd_search_time) / scalar_search_time) * 100.0;
        
        println!("  Scalar: {:.9}s", scalar_search_time);
        println!("  SIMD (est): {:.9}s", simd_search_time);
        println!("  Improvement: {:.1}%", search_improvement);
        
        println!("\nOverall Performance Summary:");
        println!("  Average improvement: {:.1}%", (rtf_improvement + md_improvement) / 2.0);
        
        if rtf_improvement >= 30.0 && md_improvement >= 30.0 {
            println!("  ✓ Target 30-50% improvement ACHIEVED!");
        } else {
            println!("  ✗ Target 30-50% improvement not yet reached");
        }
        
        println!("\n");
    }
    
    // CPU feature detection
    println!("CPU SIMD Features:");
    println!("  SSE2:  {}", is_x86_feature_detected!("sse2"));
    println!("  SSE4.2: {}", is_x86_feature_detected!("sse4.2"));
    println!("  AVX2:  {}", is_x86_feature_detected!("avx2"));
}