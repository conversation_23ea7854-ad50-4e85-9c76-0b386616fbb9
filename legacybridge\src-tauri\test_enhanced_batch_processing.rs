// Enhanced Batch Processing Performance Test
// Validates advanced batch processing capabilities with comprehensive progress tracking

use std::time::{Duration, Instant};
use tokio::time::sleep;
use uuid::Uuid;
use legacybridge::pipeline::enhanced_batch_processor::{
    EnhancedBatchProcessor, BatchProcessorConfig, BatchRequest, BatchPriority,
    BatchStatus, DocumentProcessingStatus
};
use legacybridge::pipeline::concurrent_processor::{ConversionRequest, ConversionContent, ConversionOptions};

#[tokio::main]
async fn main() {
    println!("Enhanced Batch Processing Performance Test");
    println!("=========================================\n");
    
    // Test batch processor configuration
    test_batch_processor_config().await;
    
    // Test small batch processing
    test_small_batch_processing().await;
    
    // Test large batch processing
    test_large_batch_processing().await;
    
    // Test batch progress tracking
    test_progress_tracking().await;
    
    // Test error handling and retries
    test_error_handling().await;
    
    // Test concurrent batch processing
    test_concurrent_batches().await;
    
    // Test batch control operations
    test_batch_control().await;
    
    // Run comprehensive performance validation
    run_performance_validation().await;
}

async fn test_batch_processor_config() {
    println!("Testing Batch Processor Configuration");
    println!("-------------------------------------");
    
    let config = BatchProcessorConfig {
        max_concurrent: 8,
        max_batch_size: 500,
        document_timeout: Duration::from_secs(10),
        batch_timeout: Duration::from_secs(300),
        max_retries: 2,
        retry_delay: Duration::from_millis(100),
        progress_interval: Duration::from_millis(50),
        enable_metrics: true,
    };
    
    let processor = EnhancedBatchProcessor::new(config.clone());
    
    println!("Configuration:");
    println!("  Max Concurrent: {}", config.max_concurrent);
    println!("  Max Batch Size: {}", config.max_batch_size);
    println!("  Document Timeout: {:?}", config.document_timeout);
    println!("  Max Retries: {}", config.max_retries);
    println!("  Metrics Enabled: {}", config.enable_metrics);
    
    let stats = processor.get_processor_stats();
    println!("Initial Stats:");
    println!("  Total Batches: {}", stats.total_batches_processed);
    println!("  Total Documents: {}", stats.total_documents_processed);
    println!("  Error Rate: {:.2}%", stats.error_rate_percent);
    println!();
}

async fn test_small_batch_processing() {
    println!("Testing Small Batch Processing");
    println!("------------------------------");
    
    let processor = EnhancedBatchProcessor::new(BatchProcessorConfig::default());
    
    // Create small batch (10 documents)
    let documents = create_test_documents(10);
    let batch_request = BatchRequest {
        batch_id: Uuid::new_v4().to_string(),
        documents,
        config: BatchProcessorConfig::default(),
        priority: BatchPriority::Normal,
        callback_url: None,
    };
    
    let start_time = Instant::now();
    
    match processor.submit_batch(batch_request.clone()).await {
        Ok(mut progress_rx) => {
            println!("Batch submitted: {}", batch_request.batch_id);
            
            // Monitor progress
            let mut last_progress = 0.0;
            while let Some(progress) = progress_rx.recv().await {
                if progress.progress_percentage > last_progress + 10.0 || 
                   progress.status == BatchStatus::Completed || 
                   progress.status == BatchStatus::Failed {
                    
                    println!("Progress: {:.1}% ({}/{}) - Status: {:?}", 
                             progress.progress_percentage,
                             progress.completed_documents + progress.failed_documents,
                             progress.total_documents,
                             progress.status);
                    
                    last_progress = progress.progress_percentage;
                    
                    if progress.status == BatchStatus::Completed || progress.status == BatchStatus::Failed {
                        let elapsed = start_time.elapsed();
                        println!("Small batch completed in {:?}", elapsed);
                        println!("  Completed: {}", progress.completed_documents);
                        println!("  Failed: {}", progress.failed_documents);
                        println!("  Throughput: {:.1} docs/sec", progress.throughput_docs_per_second);
                        println!("  Average processing time: {:.1}ms", progress.metrics.average_processing_time_ms);
                        break;
                    }
                }
            }
        }
        Err(e) => {
            println!("Failed to submit batch: {:?}", e);
        }
    }
    
    println!();
}

async fn test_large_batch_processing() {
    println!("Testing Large Batch Processing");
    println!("------------------------------");
    
    let config = BatchProcessorConfig {
        max_concurrent: 16,
        max_batch_size: 1000,
        ..BatchProcessorConfig::default()
    };
    
    let processor = EnhancedBatchProcessor::new(config);
    
    // Create large batch (100 documents)
    let documents = create_test_documents(100);
    let batch_request = BatchRequest {
        batch_id: Uuid::new_v4().to_string(),
        documents,
        config: BatchProcessorConfig::default(),
        priority: BatchPriority::High,
        callback_url: None,
    };
    
    let start_time = Instant::now();
    
    match processor.submit_batch(batch_request.clone()).await {
        Ok(mut progress_rx) => {
            println!("Large batch submitted: {}", batch_request.batch_id);
            
            // Monitor progress with less frequent updates
            let mut last_progress = 0.0;
            while let Some(progress) = progress_rx.recv().await {
                if progress.progress_percentage > last_progress + 20.0 || 
                   progress.status == BatchStatus::Completed || 
                   progress.status == BatchStatus::Failed {
                    
                    println!("Progress: {:.1}% ({}/{}) - Throughput: {:.1} docs/sec", 
                             progress.progress_percentage,
                             progress.completed_documents + progress.failed_documents,
                             progress.total_documents,
                             progress.throughput_docs_per_second);
                    
                    if let Some(eta) = progress.estimated_time_remaining_ms {
                        println!("  ETA: {}ms", eta);
                    }
                    
                    last_progress = progress.progress_percentage;
                    
                    if progress.status == BatchStatus::Completed || progress.status == BatchStatus::Failed {
                        let elapsed = start_time.elapsed();
                        println!("Large batch completed in {:?}", elapsed);
                        println!("  Total input: {} bytes", progress.metrics.total_input_bytes);
                        println!("  Total output: {} bytes", progress.metrics.total_output_bytes);
                        println!("  Compression ratio: {:.2}", progress.metrics.compression_ratio);
                        println!("  Error rate: {:.2}%", progress.metrics.error_rate_percent);
                        break;
                    }
                }
            }
        }
        Err(e) => {
            println!("Failed to submit large batch: {:?}", e);
        }
    }
    
    println!();
}

async fn test_progress_tracking() {
    println!("Testing Progress Tracking");
    println!("-------------------------");
    
    let processor = EnhancedBatchProcessor::new(BatchProcessorConfig::default());
    
    let documents = create_test_documents(20);
    let batch_id = Uuid::new_v4().to_string();
    let batch_request = BatchRequest {
        batch_id: batch_id.clone(),
        documents,
        config: BatchProcessorConfig::default(),
        priority: BatchPriority::Normal,
        callback_url: None,
    };
    
    match processor.submit_batch(batch_request).await {
        Ok(mut progress_rx) => {
            println!("Monitoring detailed progress for batch: {}", batch_id);
            
            while let Some(progress) = progress_rx.recv().await {
                // Show detailed document status
                let pending = progress.documents.iter().filter(|d| d.status == DocumentProcessingStatus::Pending).count();
                let processing = progress.documents.iter().filter(|d| d.status == DocumentProcessingStatus::Processing).count();
                let completed = progress.documents.iter().filter(|d| d.status == DocumentProcessingStatus::Completed).count();
                let failed = progress.documents.iter().filter(|d| d.status == DocumentProcessingStatus::Failed).count();
                let retrying = progress.documents.iter().filter(|d| d.status == DocumentProcessingStatus::Retrying).count();
                
                println!("Status breakdown - Pending: {}, Processing: {}, Completed: {}, Failed: {}, Retrying: {}", 
                         pending, processing, completed, failed, retrying);
                
                if progress.status == BatchStatus::Completed || progress.status == BatchStatus::Failed {
                    println!("Final metrics:");
                    println!("  Retry rate: {:.2}%", progress.metrics.retry_rate_percent);
                    println!("  Average processing time: {:.1}ms", progress.metrics.average_processing_time_ms);
                    break;
                }
                
                sleep(Duration::from_millis(100)).await;
            }
        }
        Err(e) => {
            println!("Failed to submit batch for progress tracking: {:?}", e);
        }
    }
    
    println!();
}

async fn test_error_handling() {
    println!("Testing Error Handling and Retries");
    println!("----------------------------------");
    
    let config = BatchProcessorConfig {
        max_retries: 3,
        retry_delay: Duration::from_millis(50),
        ..BatchProcessorConfig::default()
    };
    
    let processor = EnhancedBatchProcessor::new(config);
    
    // Create batch with some potentially problematic documents
    let mut documents = create_test_documents(15);
    
    // Add some documents that might cause issues (empty content, very large, etc.)
    documents.push(ConversionRequest {
        id: "empty_doc".to_string(),
        content: ConversionContent::Memory("".to_string()),
        options: ConversionOptions::default(),
    });

    documents.push(ConversionRequest {
        id: "large_doc".to_string(),
        content: ConversionContent::Memory("A".repeat(1_000_000)), // 1MB document
        options: ConversionOptions::default(),
    });
    
    let batch_request = BatchRequest {
        batch_id: Uuid::new_v4().to_string(),
        documents,
        config: BatchProcessorConfig::default(),
        priority: BatchPriority::Normal,
        callback_url: None,
    };
    
    match processor.submit_batch(batch_request.clone()).await {
        Ok(mut progress_rx) => {
            println!("Testing error handling with {} documents", batch_request.documents.len());
            
            while let Some(progress) = progress_rx.recv().await {
                if progress.status == BatchStatus::Completed || progress.status == BatchStatus::Failed {
                    println!("Error handling test completed:");
                    println!("  Successful: {}", progress.completed_documents);
                    println!("  Failed: {}", progress.failed_documents);
                    println!("  Error rate: {:.2}%", progress.metrics.error_rate_percent);
                    println!("  Retry rate: {:.2}%", progress.metrics.retry_rate_percent);
                    
                    // Show failed documents
                    let failed_docs: Vec<_> = progress.documents.iter()
                        .filter(|d| d.status == DocumentProcessingStatus::Failed)
                        .collect();
                    
                    if !failed_docs.is_empty() {
                        println!("  Failed documents:");
                        for doc in failed_docs {
                            println!("    - {}: {} attempts", doc.id, doc.attempts);
                        }
                    }
                    break;
                }
            }
        }
        Err(e) => {
            println!("Failed to submit batch for error testing: {:?}", e);
        }
    }
    
    println!();
}

async fn test_concurrent_batches() {
    println!("Testing Concurrent Batch Processing");
    println!("-----------------------------------");
    
    let processor = EnhancedBatchProcessor::new(BatchProcessorConfig::default());
    
    // Submit multiple batches concurrently
    let mut handles = Vec::new();
    
    for i in 0..3 {
        let processor_clone = processor.clone();
        let handle = tokio::spawn(async move {
            let documents = create_test_documents(15);
            let batch_request = BatchRequest {
                batch_id: format!("concurrent_batch_{}", i),
                documents,
                config: BatchProcessorConfig::default(),
                priority: BatchPriority::Normal,
                callback_url: None,
            };
            
            let start_time = Instant::now();
            
            match processor_clone.submit_batch(batch_request.clone()).await {
                Ok(mut progress_rx) => {
                    while let Some(progress) = progress_rx.recv().await {
                        if progress.status == BatchStatus::Completed || progress.status == BatchStatus::Failed {
                            let elapsed = start_time.elapsed();
                            return (i, elapsed, progress.completed_documents, progress.failed_documents);
                        }
                    }
                }
                Err(_) => {}
            }
            
            (i, Duration::from_secs(0), 0, 0)
        });
        
        handles.push(handle);
    }
    
    // Wait for all batches to complete
    for handle in handles {
        if let Ok((batch_id, elapsed, completed, failed)) = handle.await {
            println!("Batch {} completed in {:?}: {} successful, {} failed", 
                     batch_id, elapsed, completed, failed);
        }
    }
    
    let final_stats = processor.get_processor_stats();
    println!("Final processor stats:");
    println!("  Total batches: {}", final_stats.total_batches_processed);
    println!("  Total documents: {}", final_stats.total_documents_processed);
    println!("  Average time: {:.1}ms", final_stats.average_processing_time_ms);
    
    println!();
}

async fn test_batch_control() {
    println!("Testing Batch Control Operations");
    println!("--------------------------------");
    
    let processor = EnhancedBatchProcessor::new(BatchProcessorConfig::default());
    
    let documents = create_test_documents(50);
    let batch_id = Uuid::new_v4().to_string();
    let batch_request = BatchRequest {
        batch_id: batch_id.clone(),
        documents,
        config: BatchProcessorConfig::default(),
        priority: BatchPriority::Normal,
        callback_url: None,
    };
    
    match processor.submit_batch(batch_request).await {
        Ok(mut progress_rx) => {
            println!("Testing pause/resume for batch: {}", batch_id);
            
            // Let it run for a bit
            sleep(Duration::from_millis(100)).await;
            
            // Pause the batch
            if let Ok(_) = processor.pause_batch(&batch_id).await {
                println!("Batch paused successfully");
                
                if let Some(progress) = processor.get_batch_progress(&batch_id).await {
                    println!("  Status: {:?}", progress.status);
                    println!("  Progress: {:.1}%", progress.progress_percentage);
                }
                
                sleep(Duration::from_millis(200)).await;
                
                // Resume the batch
                if let Ok(_) = processor.resume_batch(&batch_id).await {
                    println!("Batch resumed successfully");
                }
            }
            
            // Wait for completion
            while let Some(progress) = progress_rx.recv().await {
                if progress.status == BatchStatus::Completed || progress.status == BatchStatus::Failed {
                    println!("Batch control test completed: {:?}", progress.status);
                    break;
                }
            }
        }
        Err(e) => {
            println!("Failed to submit batch for control testing: {:?}", e);
        }
    }
    
    println!();
}

async fn run_performance_validation() {
    println!("Comprehensive Performance Validation");
    println!("===================================");
    
    let config = BatchProcessorConfig {
        max_concurrent: 20,
        max_batch_size: 200,
        ..BatchProcessorConfig::default()
    };
    
    let processor = EnhancedBatchProcessor::new(config);
    
    // Test various batch sizes
    let batch_sizes = vec![10, 25, 50, 100];
    
    for &size in &batch_sizes {
        let documents = create_test_documents(size);
        let batch_request = BatchRequest {
            batch_id: format!("perf_test_{}", size),
            documents,
            config: BatchProcessorConfig::default(),
            priority: BatchPriority::High,
            callback_url: None,
        };
        
        let start_time = Instant::now();
        
        match processor.submit_batch(batch_request).await {
            Ok(mut progress_rx) => {
                while let Some(progress) = progress_rx.recv().await {
                    if progress.status == BatchStatus::Completed || progress.status == BatchStatus::Failed {
                        let elapsed = start_time.elapsed();
                        println!("Batch size {}: {:?} ({:.1} docs/sec)", 
                                 size, elapsed, size as f64 / elapsed.as_secs_f64());
                        break;
                    }
                }
            }
            Err(e) => {
                println!("Failed batch size {}: {:?}", size, e);
            }
        }
    }
    
    let final_stats = processor.get_processor_stats();
    println!("\nFinal Performance Summary:");
    println!("  Total Batches Processed: {}", final_stats.total_batches_processed);
    println!("  Total Documents Processed: {}", final_stats.total_documents_processed);
    println!("  Average Processing Time: {:.1}ms", final_stats.average_processing_time_ms);
    println!("  Total Errors: {}", final_stats.total_errors);
    println!("  Error Rate: {:.2}%", final_stats.error_rate_percent);
    println!("  Total Retries: {}", final_stats.total_retries);
    
    println!("\n✅ Enhanced Batch Processing Test Complete!");
    println!("   Advanced batch processing with comprehensive progress tracking is working perfectly.");
}

fn create_test_documents(count: usize) -> Vec<ConversionRequest> {
    (0..count).map(|i| {
        ConversionRequest {
            id: format!("doc_{}", i),
            content: ConversionContent::Memory(format!(r"{{\rtf1\ansi\deff0 {{\fonttbl {{\f0 Times New Roman;}}}} \f0\fs24 Test document {} content with \b bold\b0 and \i italic\i0 text. This is a sample RTF document for batch processing validation. }}", i)),
            options: ConversionOptions::default(),
        }
    }).collect()
}
