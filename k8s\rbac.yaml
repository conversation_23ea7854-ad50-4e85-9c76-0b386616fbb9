# RBAC Configuration for LegacyBridge
apiVersion: v1
kind: Namespace
metadata:
  name: legacybridge
  labels:
    name: legacybridge
    app.kubernetes.io/name: legacybridge
    app.kubernetes.io/part-of: legacybridge
  annotations:
    description: "LegacyBridge MCP Server Namespace"
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: legacybridge
  namespace: legacybridge
  labels:
    app: legacybridge
    app.kubernetes.io/name: legacybridge
    app.kubernetes.io/component: mcp-server
    app.kubernetes.io/part-of: legacybridge
  annotations:
    description: "Service account for LegacyBridge MCP Server"
automountServiceAccountToken: true
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: legacybridge
  namespace: legacybridge
  labels:
    app: legacybridge
    app.kubernetes.io/name: legacybridge
    app.kubernetes.io/component: mcp-server
rules:
- apiGroups: [""]
  resources: ["configmaps"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["secrets"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["services"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["deployments"]
  verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: legacybridge
  namespace: legacybridge
  labels:
    app: legacybridge
    app.kubernetes.io/name: legacybridge
    app.kubernetes.io/component: mcp-server
subjects:
- kind: ServiceAccount
  name: legacybridge
  namespace: legacybridge
roleRef:
  kind: Role
  name: legacybridge
  apiGroup: rbac.authorization.k8s.io
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: legacybridge-config
  namespace: legacybridge
  labels:
    app: legacybridge
    app.kubernetes.io/name: legacybridge
    app.kubernetes.io/component: config
data:
  # Application Configuration
  NODE_ENV: "production"
  LOG_LEVEL: "info"
  CACHE_ENABLED: "true"
  ENABLE_DOC: "false"
  ENABLE_WORDPERFECT: "false"
  
  # Performance Configuration
  NODE_OPTIONS: "--max-old-space-size=768 --enable-source-maps=false"
  UV_THREADPOOL_SIZE: "4"
  
  # Security Configuration
  HELMET_ENABLED: "true"
  CORS_ENABLED: "true"
  RATE_LIMIT_ENABLED: "true"
  
  # Monitoring Configuration
  METRICS_ENABLED: "true"
  HEALTH_CHECK_ENABLED: "true"
  PROMETHEUS_METRICS: "true"
  
  # Feature Flags
  FEATURE_BATCH_PROCESSING: "true"
  FEATURE_ASYNC_CONVERSION: "true"
  FEATURE_CACHING: "true"
  
  # Timeouts and Limits
  REQUEST_TIMEOUT: "30000"
  MAX_FILE_SIZE: "10485760"
  MAX_CONCURRENT_REQUESTS: "100"
---
apiVersion: v1
kind: Secret
metadata:
  name: legacybridge-secrets
  namespace: legacybridge
  labels:
    app: legacybridge
    app.kubernetes.io/name: legacybridge
    app.kubernetes.io/component: secrets
type: Opaque
data:
  # Base64 encoded secrets (replace with actual values)
  # API_KEY: <base64-encoded-api-key>
  # DATABASE_URL: <base64-encoded-database-url>
  # JWT_SECRET: <base64-encoded-jwt-secret>
  # ENCRYPTION_KEY: <base64-encoded-encryption-key>
stringData:
  # Example secrets (replace with actual values in production)
  API_KEY: "your-api-key-here"
  JWT_SECRET: "your-jwt-secret-here"
  ENCRYPTION_KEY: "your-encryption-key-here"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: legacybridge-nginx-config
  namespace: legacybridge
  labels:
    app: legacybridge
    app.kubernetes.io/name: legacybridge
    app.kubernetes.io/component: nginx-config
data:
  nginx.conf: |
    upstream legacybridge {
        least_conn;
        server legacybridge:80 max_fails=3 fail_timeout=30s;
    }
    
    server {
        listen 80;
        server_name _;
        
        # Security headers
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
        
        # Rate limiting
        limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
        limit_req zone=api burst=20 nodelay;
        
        # Gzip compression
        gzip on;
        gzip_vary on;
        gzip_min_length 1024;
        gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
        
        # Health check endpoint
        location /health {
            access_log off;
            proxy_pass http://legacybridge/health;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # API endpoints
        location /api/ {
            proxy_pass http://legacybridge/api/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Timeouts
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
            
            # Buffer settings
            proxy_buffering on;
            proxy_buffer_size 4k;
            proxy_buffers 8 4k;
        }
        
        # Static files
        location / {
            proxy_pass http://legacybridge/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Cache static assets
            location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
                expires 1y;
                add_header Cache-Control "public, immutable";
            }
        }
    }
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: legacybridge-monitoring-config
  namespace: legacybridge
  labels:
    app: legacybridge
    app.kubernetes.io/name: legacybridge
    app.kubernetes.io/component: monitoring-config
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 15s
    
    scrape_configs:
    - job_name: 'legacybridge'
      static_configs:
      - targets: ['legacybridge:9090']
      scrape_interval: 30s
      metrics_path: /metrics
      
    - job_name: 'kubernetes-pods'
      kubernetes_sd_configs:
      - role: pod
        namespaces:
          names:
          - legacybridge
      relabel_configs:
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
        action: keep
        regex: true
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
        action: replace
        target_label: __metrics_path__
        regex: (.+)
      - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
        action: replace
        regex: ([^:]+)(?::\d+)?;(\d+)
        replacement: $1:$2
        target_label: __address__
      - action: labelmap
        regex: __meta_kubernetes_pod_label_(.+)
      - source_labels: [__meta_kubernetes_namespace]
        action: replace
        target_label: kubernetes_namespace
      - source_labels: [__meta_kubernetes_pod_name]
        action: replace
        target_label: kubernetes_pod_name
  
  alerting_rules.yml: |
    groups:
    - name: legacybridge
      rules:
      - alert: LegacyBridgeDown
        expr: up{job="legacybridge"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "LegacyBridge service is down"
          description: "LegacyBridge service has been down for more than 1 minute"
      
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value }} errors per second"
      
      - alert: HighMemoryUsage
        expr: container_memory_usage_bytes{pod=~"legacybridge-.*"} / container_spec_memory_limit_bytes > 0.9
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage"
          description: "Memory usage is above 90%"
      
      - alert: HighCPUUsage
        expr: rate(container_cpu_usage_seconds_total{pod=~"legacybridge-.*"}[5m]) > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage"
          description: "CPU usage is above 80%"
