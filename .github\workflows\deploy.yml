name: Deployment Pipeline

on:
  workflow_call:
    inputs:
      environment:
        description: 'Target environment'
        required: true
        type: string
      image_tag:
        description: 'Docker image tag to deploy'
        required: true
        type: string
      skip_health_checks:
        description: 'Skip health checks (emergency only)'
        required: false
        default: false
        type: boolean
    secrets:
      KUBE_CONFIG:
        description: 'Kubernetes configuration'
        required: true
      SLACK_WEBHOOK:
        description: 'Slack webhook for notifications'
        required: false

  workflow_dispatch:
    inputs:
      environment:
        description: 'Target environment'
        required: true
        type: choice
        options:
        - staging
        - production
      image_tag:
        description: 'Docker image tag to deploy'
        required: true
        type: string
      deployment_strategy:
        description: 'Deployment strategy'
        required: false
        default: 'blue-green'
        type: choice
        options:
        - blue-green
        - rolling
        - canary
      skip_health_checks:
        description: 'Skip health checks (emergency only)'
        required: false
        default: false
        type: boolean

env:
  KUBECTL_VERSION: 'v1.28.0'
  HEALTH_CHECK_TIMEOUT: 300
  ROLLBACK_TIMEOUT: 180

jobs:
  # Pre-deployment validation
  pre-deployment:
    name: Pre-Deployment Validation
    runs-on: ubuntu-latest
    outputs:
      deployment-id: ${{ steps.generate-id.outputs.deployment-id }}
      current-version: ${{ steps.current-version.outputs.version }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Generate deployment ID
        id: generate-id
        run: |
          DEPLOYMENT_ID="deploy-$(date +%Y%m%d-%H%M%S)-$(echo ${{ github.sha }} | cut -c1-8)"
          echo "deployment-id=${DEPLOYMENT_ID}" >> $GITHUB_OUTPUT
          echo "Generated deployment ID: ${DEPLOYMENT_ID}"

      - name: Setup kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: ${{ env.KUBECTL_VERSION }}

      - name: Configure kubectl
        run: |
          echo "${{ secrets.KUBE_CONFIG }}" | base64 -d > kubeconfig
          export KUBECONFIG=kubeconfig
          kubectl cluster-info

      - name: Get current version
        id: current-version
        run: |
          export KUBECONFIG=kubeconfig
          CURRENT_VERSION=$(kubectl get deployment legacybridge-${{ inputs.environment }} -o jsonpath='{.spec.template.spec.containers[0].image}' 2>/dev/null || echo "none")
          echo "version=${CURRENT_VERSION}" >> $GITHUB_OUTPUT
          echo "Current version: ${CURRENT_VERSION}"

      - name: Validate image exists
        run: |
          # Check if the image exists in the registry
          docker manifest inspect ${{ inputs.image_tag }} > /dev/null
          echo "✅ Image ${{ inputs.image_tag }} exists and is accessible"

      - name: Check cluster resources
        run: |
          export KUBECONFIG=kubeconfig
          echo "Checking cluster resources..."
          kubectl top nodes || echo "⚠️ Metrics server not available"
          kubectl get nodes -o wide
          echo "✅ Cluster is accessible and healthy"

  # Blue-Green Deployment
  deploy-blue-green:
    name: Blue-Green Deployment
    runs-on: ubuntu-latest
    needs: pre-deployment
    if: inputs.deployment_strategy == 'blue-green' || inputs.deployment_strategy == ''
    environment:
      name: ${{ inputs.environment }}
      url: ${{ steps.get-url.outputs.url }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: ${{ env.KUBECTL_VERSION }}

      - name: Configure kubectl
        run: |
          echo "${{ secrets.KUBE_CONFIG }}" | base64 -d > kubeconfig
          export KUBECONFIG=kubeconfig

      - name: Determine current and target colors
        id: colors
        run: |
          export KUBECONFIG=kubeconfig
          
          # Check which color is currently active
          CURRENT_COLOR=$(kubectl get service legacybridge-${{ inputs.environment }} -o jsonpath='{.spec.selector.color}' 2>/dev/null || echo "blue")
          
          if [[ "${CURRENT_COLOR}" == "blue" ]]; then
            TARGET_COLOR="green"
          else
            TARGET_COLOR="blue"
          fi
          
          echo "current-color=${CURRENT_COLOR}" >> $GITHUB_OUTPUT
          echo "target-color=${TARGET_COLOR}" >> $GITHUB_OUTPUT
          echo "Current: ${CURRENT_COLOR}, Target: ${TARGET_COLOR}"

      - name: Deploy to target environment
        run: |
          export KUBECONFIG=kubeconfig
          
          # Update the target deployment
          kubectl set image deployment/legacybridge-${{ inputs.environment }}-${{ steps.colors.outputs.target-color }} \
            legacybridge=${{ inputs.image_tag }} \
            --record
          
          # Add deployment labels
          kubectl label deployment/legacybridge-${{ inputs.environment }}-${{ steps.colors.outputs.target-color }} \
            deployment-id=${{ needs.pre-deployment.outputs.deployment-id }} \
            --overwrite

      - name: Wait for deployment rollout
        run: |
          export KUBECONFIG=kubeconfig
          
          echo "Waiting for deployment to complete..."
          kubectl rollout status deployment/legacybridge-${{ inputs.environment }}-${{ steps.colors.outputs.target-color }} \
            --timeout=${HEALTH_CHECK_TIMEOUT}s

      - name: Run health checks
        if: '!inputs.skip_health_checks'
        run: |
          export KUBECONFIG=kubeconfig
          
          # Get pod IP for direct health checks
          POD_IP=$(kubectl get pods -l app=legacybridge,color=${{ steps.colors.outputs.target-color }} -o jsonpath='{.items[0].status.podIP}')
          
          echo "Running health checks on pod ${POD_IP}..."
          
          # Port forward for testing
          kubectl port-forward deployment/legacybridge-${{ inputs.environment }}-${{ steps.colors.outputs.target-color }} 8080:3030 &
          PF_PID=$!
          
          sleep 10
          
          # Health check endpoints
          curl -f http://localhost:8080/health || exit 1
          curl -f http://localhost:8080/api/status || exit 1
          
          # Kill port forward
          kill $PF_PID
          
          echo "✅ Health checks passed"

      - name: Switch traffic to new version
        run: |
          export KUBECONFIG=kubeconfig
          
          echo "Switching traffic to ${{ steps.colors.outputs.target-color }} deployment..."
          
          # Update service selector
          kubectl patch service legacybridge-${{ inputs.environment }} \
            -p '{"spec":{"selector":{"color":"${{ steps.colors.outputs.target-color }}"}}}'
          
          echo "✅ Traffic switched to new version"

      - name: Monitor new deployment
        run: |
          export KUBECONFIG=kubeconfig
          
          echo "Monitoring new deployment for 2 minutes..."
          sleep 120
          
          # Check if pods are still healthy
          kubectl get pods -l app=legacybridge,color=${{ steps.colors.outputs.target-color }}
          
          # Check service endpoints
          kubectl get endpoints legacybridge-${{ inputs.environment }}
          
          echo "✅ Monitoring completed successfully"

      - name: Scale down old deployment
        run: |
          export KUBECONFIG=kubeconfig
          
          echo "Scaling down old ${{ steps.colors.outputs.current-color }} deployment..."
          kubectl scale deployment legacybridge-${{ inputs.environment }}-${{ steps.colors.outputs.current-color }} --replicas=0
          
          echo "✅ Old deployment scaled down"

      - name: Get deployment URL
        id: get-url
        run: |
          if [[ "${{ inputs.environment }}" == "production" ]]; then
            echo "url=https://legacybridge.com" >> $GITHUB_OUTPUT
          else
            echo "url=https://${{ inputs.environment }}.legacybridge.com" >> $GITHUB_OUTPUT
          fi

  # Rolling Deployment (alternative strategy)
  deploy-rolling:
    name: Rolling Deployment
    runs-on: ubuntu-latest
    needs: pre-deployment
    if: inputs.deployment_strategy == 'rolling'
    environment:
      name: ${{ inputs.environment }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: ${{ env.KUBECTL_VERSION }}

      - name: Configure kubectl
        run: |
          echo "${{ secrets.KUBE_CONFIG }}" | base64 -d > kubeconfig
          export KUBECONFIG=kubeconfig

      - name: Rolling update deployment
        run: |
          export KUBECONFIG=kubeconfig
          
          # Perform rolling update
          kubectl set image deployment/legacybridge-${{ inputs.environment }} \
            legacybridge=${{ inputs.image_tag }} \
            --record
          
          # Wait for rollout
          kubectl rollout status deployment/legacybridge-${{ inputs.environment }} \
            --timeout=${HEALTH_CHECK_TIMEOUT}s

      - name: Verify deployment
        if: '!inputs.skip_health_checks'
        run: |
          export KUBECONFIG=kubeconfig
          
          # Port forward for testing
          kubectl port-forward deployment/legacybridge-${{ inputs.environment }} 8080:3030 &
          PF_PID=$!
          
          sleep 10
          
          # Health checks
          curl -f http://localhost:8080/health || exit 1
          curl -f http://localhost:8080/api/status || exit 1
          
          kill $PF_PID
          echo "✅ Rolling deployment verified"

  # Post-deployment tasks
  post-deployment:
    name: Post-Deployment Tasks
    runs-on: ubuntu-latest
    needs: [pre-deployment, deploy-blue-green, deploy-rolling]
    if: always() && (needs.deploy-blue-green.result == 'success' || needs.deploy-rolling.result == 'success')
    steps:
      - name: Run smoke tests
        run: |
          if [[ "${{ inputs.environment }}" == "production" ]]; then
            URL="https://legacybridge.com"
          else
            URL="https://${{ inputs.environment }}.legacybridge.com"
          fi
          
          echo "Running smoke tests against ${URL}..."
          
          # Basic connectivity
          curl -f ${URL}/health || exit 1
          
          # API functionality
          curl -f ${URL}/api/status || exit 1
          
          echo "✅ Smoke tests passed"

      - name: Update deployment tracking
        run: |
          echo "## 🚀 Deployment Complete" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Environment:** ${{ inputs.environment }}" >> $GITHUB_STEP_SUMMARY
          echo "**Image:** ${{ inputs.image_tag }}" >> $GITHUB_STEP_SUMMARY
          echo "**Deployment ID:** ${{ needs.pre-deployment.outputs.deployment-id }}" >> $GITHUB_STEP_SUMMARY
          echo "**Previous Version:** ${{ needs.pre-deployment.outputs.current-version }}" >> $GITHUB_STEP_SUMMARY
          echo "**Strategy:** ${{ inputs.deployment_strategy || 'blue-green' }}" >> $GITHUB_STEP_SUMMARY
          echo "**Status:** ✅ Success" >> $GITHUB_STEP_SUMMARY

      - name: Notify team
        if: secrets.SLACK_WEBHOOK
        run: |
          curl -X POST -H 'Content-type: application/json' \
            --data '{"text":"🚀 LegacyBridge deployed to ${{ inputs.environment }}\nImage: ${{ inputs.image_tag }}\nDeployment ID: ${{ needs.pre-deployment.outputs.deployment-id }}"}' \
            ${{ secrets.SLACK_WEBHOOK }}

  # Rollback job (can be triggered manually)
  rollback:
    name: Emergency Rollback
    runs-on: ubuntu-latest
    if: failure() && needs.pre-deployment.outputs.current-version != 'none'
    needs: [pre-deployment, deploy-blue-green, deploy-rolling]
    steps:
      - name: Setup kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: ${{ env.KUBECTL_VERSION }}

      - name: Configure kubectl
        run: |
          echo "${{ secrets.KUBE_CONFIG }}" | base64 -d > kubeconfig
          export KUBECONFIG=kubeconfig

      - name: Rollback deployment
        run: |
          export KUBECONFIG=kubeconfig
          
          echo "🚨 Performing emergency rollback..."
          
          # Rollback to previous version
          kubectl rollout undo deployment/legacybridge-${{ inputs.environment }}
          
          # Wait for rollback to complete
          kubectl rollout status deployment/legacybridge-${{ inputs.environment }} \
            --timeout=${ROLLBACK_TIMEOUT}s
          
          echo "✅ Rollback completed"

      - name: Verify rollback
        run: |
          if [[ "${{ inputs.environment }}" == "production" ]]; then
            URL="https://legacybridge.com"
          else
            URL="https://${{ inputs.environment }}.legacybridge.com"
          fi
          
          sleep 30
          curl -f ${URL}/health || exit 1
          echo "✅ Rollback verified"
