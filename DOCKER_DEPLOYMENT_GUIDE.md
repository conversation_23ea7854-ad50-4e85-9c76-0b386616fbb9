# LegacyBridge Docker Deployment Guide

## Overview

This guide covers the Docker containerization of LegacyBridge, focusing on the MCP (Model Context Protocol) server deployment for production environments.

## Architecture

LegacyBridge supports multiple deployment modes:
- **Desktop Application**: Tauri-based GUI application
- **MCP Server**: Web service for Model Context Protocol integration
- **CLI Tool**: Command-line interface with serve mode
- **Next.js Web App**: Standalone web application

For Docker deployment, we focus on the **MCP Server** mode, which provides a robust web API for RTF-Markdown conversion.

## Docker Images

### Production Image (`Dockerfile.optimized`)

Multi-stage build optimized for production:
- **Base**: Node.js 20 Alpine Linux
- **Security**: Non-root user, minimal dependencies
- **Size**: Optimized for minimal image size
- **Performance**: Production-ready with health checks

### Build Process

```bash
# Build the optimized production image
docker build -f Dockerfile.optimized -t legacybridge:latest .

# Build with version information
docker build -f Dockerfile.optimized \
  --build-arg VERSION=1.0.0 \
  --build-arg BUILD_DATE=$(date -u +'%Y-%m-%dT%H:%M:%SZ') \
  --build-arg COMMIT_SHA=$(git rev-parse HEAD) \
  -t legacybridge:1.0.0 .
```

## Environment Variables

### Required Variables
- `NODE_ENV`: Environment mode (production/development)
- `MCP_PORT`: Server port (default: 3030)
- `LOG_LEVEL`: Logging level (error/warn/info/debug)

### Optional Variables
- `CACHE_ENABLED`: Enable caching (true/false)
- `CACHE_TYPE`: Cache type (memory/redis)
- `REDIS_URL`: Redis connection string
- `API_KEYS`: Comma-separated API keys for authentication
- `ENABLE_DOC`: Enable DOC format support (requires LibreOffice)
- `ENABLE_WORDPERFECT`: Enable WordPerfect support

### Security Variables
- `JWT_SECRET`: Secret for JWT token signing
- `ENCRYPTION_KEY`: Key for data encryption
- `RATE_LIMIT_WINDOW`: Rate limiting window (ms)
- `RATE_LIMIT_MAX`: Maximum requests per window

## Running the Container

### Basic Usage

```bash
# Run with default settings
docker run -p 3030:3030 legacybridge:latest

# Run with custom environment
docker run -p 3030:3030 \
  -e NODE_ENV=production \
  -e LOG_LEVEL=info \
  -e MCP_PORT=3030 \
  legacybridge:latest
```

### Production Deployment

```bash
# Run with production configuration
docker run -d \
  --name legacybridge-mcp \
  --restart unless-stopped \
  -p 3030:3030 \
  -e NODE_ENV=production \
  -e LOG_LEVEL=warn \
  -e CACHE_ENABLED=true \
  -e API_KEYS=your-secure-api-key \
  -v legacybridge-logs:/app/logs \
  -v legacybridge-uploads:/app/uploads \
  legacybridge:latest
```

### With Docker Compose

Use the provided `docker-compose.yml` for full stack deployment:

```bash
# Start all services
docker-compose up -d

# Start only the MCP server
docker-compose up -d legacybridge

# View logs
docker-compose logs -f legacybridge
```

## Health Checks

The container includes built-in health checks:

```bash
# Check container health
docker ps

# Manual health check
curl http://localhost:3030/health
```

Expected response:
```json
{
  "status": "healthy",
  "version": "1.0.0",
  "uptime": 123.45
}
```

## API Endpoints

### Core Endpoints
- `GET /health` - Health check
- `GET /metrics` - Performance metrics
- `POST /mcp/convert` - Convert RTF to Markdown
- `POST /mcp/batch` - Batch conversion
- `GET /mcp/formats` - Supported formats

### WebSocket
- `WS /ws/conversion` - Real-time conversion updates

## Security Considerations

### Container Security
- Runs as non-root user (`mcpuser`)
- Minimal Alpine Linux base image
- No unnecessary packages or tools
- Security scanning with Trivy

### Network Security
- Expose only necessary ports
- Use reverse proxy (nginx/traefik) for SSL termination
- Implement rate limiting
- Use API keys for authentication

### Data Security
- Temporary files cleaned automatically
- No persistent storage of converted content
- Input validation and sanitization
- Memory limits to prevent DoS attacks

## Monitoring and Logging

### Logging
- Structured JSON logging
- Configurable log levels
- Container logs via Docker logging drivers
- Persistent log storage via volumes

### Metrics
- Performance metrics endpoint
- Memory and CPU usage tracking
- Conversion statistics
- Error rate monitoring

### Integration
- Prometheus metrics export
- Grafana dashboard templates
- Health check endpoints for load balancers

## Troubleshooting

### Common Issues

1. **Container won't start**
   ```bash
   # Check logs
   docker logs legacybridge-mcp
   
   # Check environment variables
   docker exec legacybridge-mcp env
   ```

2. **Port conflicts**
   ```bash
   # Use different port
   docker run -p 8080:3030 legacybridge:latest
   ```

3. **Memory issues**
   ```bash
   # Set memory limits
   docker run --memory=512m legacybridge:latest
   ```

### Debug Mode

```bash
# Run debug image with shell access
docker build -f Dockerfile.optimized --target debug -t legacybridge:debug .
docker run -it legacybridge:debug sh
```

## Performance Optimization

### Resource Limits
```bash
# Set CPU and memory limits
docker run \
  --cpus="1.0" \
  --memory="512m" \
  --memory-swap="1g" \
  legacybridge:latest
```

### Scaling
```bash
# Run multiple instances
docker-compose up --scale legacybridge=3
```

## Backup and Recovery

### Data Volumes
- `/app/logs` - Application logs
- `/app/uploads` - Temporary upload storage
- `/app/output` - Conversion output cache

### Backup Strategy
```bash
# Backup volumes
docker run --rm -v legacybridge-logs:/data -v $(pwd):/backup alpine tar czf /backup/logs-backup.tar.gz /data

# Restore volumes
docker run --rm -v legacybridge-logs:/data -v $(pwd):/backup alpine tar xzf /backup/logs-backup.tar.gz -C /
```

## Next Steps

1. **Production Deployment**: Set up load balancer and SSL termination
2. **Monitoring**: Configure Prometheus and Grafana
3. **CI/CD**: Automate builds and deployments
4. **Security Audit**: Regular security scans and updates
5. **Performance Testing**: Load testing and optimization
