name: Security and Quality Gates

on:
  pull_request:
    branches: [ main, develop ]
  push:
    branches: [ main, develop ]
  schedule:
    # Run security scans daily at 3 AM UTC
    - cron: '0 3 * * *'
  workflow_dispatch:
    inputs:
      scan_type:
        description: 'Type of security scan to run'
        required: false
        default: 'all'
        type: choice
        options:
        - all
        - dependencies
        - code
        - container
        - secrets

env:
  CARGO_TERM_COLOR: always
  NODE_VERSION: '20'

jobs:
  # Dependency vulnerability scanning
  dependency-scan:
    name: Dependency Security Scan
    runs-on: ubuntu-latest
    if: inputs.scan_type == 'all' || inputs.scan_type == 'dependencies' || inputs.scan_type == ''
    outputs:
      vulnerabilities-found: ${{ steps.audit-results.outputs.vulnerabilities }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: 'legacybridge/package-lock.json'

      - name: Setup Rust
        uses: actions-rs/toolchain@v1
        with:
          toolchain: stable
          override: true

      - name: Install dependencies
        working-directory: legacybridge
        run: npm ci

      - name: Run npm audit
        id: npm-audit
        working-directory: legacybridge
        run: |
          npm audit --audit-level=moderate --json > npm-audit.json || true
          VULNERABILITIES=$(jq '.metadata.vulnerabilities.total' npm-audit.json)
          echo "npm-vulnerabilities=${VULNERABILITIES}" >> $GITHUB_OUTPUT
          
          if [ "$VULNERABILITIES" -gt 0 ]; then
            echo "Found $VULNERABILITIES npm vulnerabilities"
            jq '.advisories' npm-audit.json
          fi

      - name: Install cargo-audit
        run: cargo install cargo-audit

      - name: Run cargo audit
        id: cargo-audit
        working-directory: legacybridge/src-tauri
        run: |
          cargo audit --json > cargo-audit.json || true
          VULNERABILITIES=$(jq '.vulnerabilities.count' cargo-audit.json 2>/dev/null || echo "0")
          echo "cargo-vulnerabilities=${VULNERABILITIES}" >> $GITHUB_OUTPUT
          
          if [ "$VULNERABILITIES" -gt 0 ]; then
            echo "Found $VULNERABILITIES cargo vulnerabilities"
            jq '.vulnerabilities.list' cargo-audit.json
          fi

      - name: Check dependency licenses
        working-directory: legacybridge
        run: |
          npx license-checker --json > licenses.json
          
          # Check for problematic licenses
          PROBLEMATIC_LICENSES=$(jq -r 'to_entries[] | select(.value.licenses | test("GPL|AGPL|LGPL")) | .key' licenses.json || echo "")
          
          if [ -n "$PROBLEMATIC_LICENSES" ]; then
            echo "⚠️ Found packages with problematic licenses:"
            echo "$PROBLEMATIC_LICENSES"
          else
            echo "✅ All licenses are acceptable"
          fi

      - name: Aggregate audit results
        id: audit-results
        run: |
          NPM_VULNS=${{ steps.npm-audit.outputs.npm-vulnerabilities }}
          CARGO_VULNS=${{ steps.cargo-audit.outputs.cargo-vulnerabilities }}
          TOTAL_VULNS=$((NPM_VULNS + CARGO_VULNS))
          
          echo "vulnerabilities=${TOTAL_VULNS}" >> $GITHUB_OUTPUT
          
          if [ "$TOTAL_VULNS" -gt 0 ]; then
            echo "❌ Found $TOTAL_VULNS total vulnerabilities"
            exit 1
          else
            echo "✅ No vulnerabilities found"
          fi

      - name: Upload audit results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: dependency-audit-results
          path: |
            legacybridge/npm-audit.json
            legacybridge/src-tauri/cargo-audit.json
            legacybridge/licenses.json
          retention-days: 30

  # Static code analysis
  code-analysis:
    name: Static Code Analysis
    runs-on: ubuntu-latest
    if: inputs.scan_type == 'all' || inputs.scan_type == 'code' || inputs.scan_type == ''
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: 'legacybridge/package-lock.json'

      - name: Setup Rust
        uses: actions-rs/toolchain@v1
        with:
          toolchain: stable
          override: true
          components: clippy, rustfmt

      - name: Install dependencies
        working-directory: legacybridge
        run: npm ci

      - name: Run ESLint
        working-directory: legacybridge
        run: |
          npx eslint . --ext .js,.jsx,.ts,.tsx --format json --output-file eslint-results.json || true
          
          ERRORS=$(jq '[.[] | .errorCount] | add' eslint-results.json)
          WARNINGS=$(jq '[.[] | .warningCount] | add' eslint-results.json)
          
          echo "ESLint found $ERRORS errors and $WARNINGS warnings"
          
          if [ "$ERRORS" -gt 0 ]; then
            echo "❌ ESLint found $ERRORS errors"
            jq '.[] | select(.errorCount > 0)' eslint-results.json
            exit 1
          fi

      - name: Run Rust Clippy
        working-directory: legacybridge/src-tauri
        run: |
          cargo clippy --all-targets --all-features --message-format=json -- -D warnings > clippy-results.json || true
          
          # Check for clippy errors
          if grep -q '"level":"error"' clippy-results.json; then
            echo "❌ Clippy found errors"
            grep '"level":"error"' clippy-results.json
            exit 1
          else
            echo "✅ No clippy errors found"
          fi

      - name: Check Rust formatting
        working-directory: legacybridge/src-tauri
        run: |
          cargo fmt --all -- --check
          echo "✅ Rust code is properly formatted"

      - name: Run TypeScript type checking
        working-directory: legacybridge
        run: |
          npx tsc --noEmit --pretty false > tsc-results.txt 2>&1 || true
          
          if [ -s tsc-results.txt ]; then
            echo "❌ TypeScript type errors found:"
            cat tsc-results.txt
            exit 1
          else
            echo "✅ No TypeScript type errors"
          fi

      - name: Initialize CodeQL
        uses: github/codeql-action/init@v3
        with:
          languages: javascript, rust
          queries: security-and-quality

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3

      - name: Upload code analysis results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: code-analysis-results
          path: |
            legacybridge/eslint-results.json
            legacybridge/src-tauri/clippy-results.json
            legacybridge/tsc-results.txt
          retention-days: 30

  # Secret scanning
  secret-scan:
    name: Secret Scanning
    runs-on: ubuntu-latest
    if: inputs.scan_type == 'all' || inputs.scan_type == 'secrets' || inputs.scan_type == ''
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Run TruffleHog
        uses: trufflesecurity/trufflehog@main
        with:
          path: ./
          base: main
          head: HEAD
          extra_args: --debug --only-verified

      - name: Run GitLeaks
        uses: gitleaks/gitleaks-action@v2
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  # Container security scanning
  container-scan:
    name: Container Security Scan
    runs-on: ubuntu-latest
    if: inputs.scan_type == 'all' || inputs.scan_type == 'container' || inputs.scan_type == ''
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build container image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile.optimized
          push: false
          tags: legacybridge:security-scan
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: 'legacybridge:security-scan'
          format: 'sarif'
          output: 'trivy-results.sarif'
          severity: 'CRITICAL,HIGH,MEDIUM'

      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

      - name: Run Snyk container scan
        uses: snyk/actions/docker@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          image: legacybridge:security-scan
          args: --severity-threshold=high
        continue-on-error: true

      - name: Upload container scan results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: container-scan-results
          path: |
            trivy-results.sarif
            snyk.sarif
          retention-days: 30

  # Quality gates enforcement
  quality-gates:
    name: Quality Gates
    runs-on: ubuntu-latest
    needs: [dependency-scan, code-analysis, secret-scan, container-scan]
    if: always()
    steps:
      - name: Check security scan results
        run: |
          echo "## 🔒 Security and Quality Gates Results" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          
          # Check dependency scan
          if [[ "${{ needs.dependency-scan.result }}" == "failure" ]]; then
            echo "❌ **Dependency Scan**: FAILED" >> $GITHUB_STEP_SUMMARY
            echo "- Vulnerabilities found: ${{ needs.dependency-scan.outputs.vulnerabilities-found }}" >> $GITHUB_STEP_SUMMARY
            GATE_FAILED=true
          else
            echo "✅ **Dependency Scan**: PASSED" >> $GITHUB_STEP_SUMMARY
          fi
          
          # Check code analysis
          if [[ "${{ needs.code-analysis.result }}" == "failure" ]]; then
            echo "❌ **Code Analysis**: FAILED" >> $GITHUB_STEP_SUMMARY
            GATE_FAILED=true
          else
            echo "✅ **Code Analysis**: PASSED" >> $GITHUB_STEP_SUMMARY
          fi
          
          # Check secret scan
          if [[ "${{ needs.secret-scan.result }}" == "failure" ]]; then
            echo "❌ **Secret Scan**: FAILED" >> $GITHUB_STEP_SUMMARY
            GATE_FAILED=true
          else
            echo "✅ **Secret Scan**: PASSED" >> $GITHUB_STEP_SUMMARY
          fi
          
          # Check container scan
          if [[ "${{ needs.container-scan.result }}" == "failure" ]]; then
            echo "❌ **Container Scan**: FAILED" >> $GITHUB_STEP_SUMMARY
            GATE_FAILED=true
          else
            echo "✅ **Container Scan**: PASSED" >> $GITHUB_STEP_SUMMARY
          fi
          
          echo "" >> $GITHUB_STEP_SUMMARY
          
          if [[ "$GATE_FAILED" == "true" ]]; then
            echo "🚫 **Overall Status**: QUALITY GATES FAILED" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "**Action Required**: Fix the issues above before deployment can proceed." >> $GITHUB_STEP_SUMMARY
            exit 1
          else
            echo "✅ **Overall Status**: ALL QUALITY GATES PASSED" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "**Status**: Ready for deployment 🚀" >> $GITHUB_STEP_SUMMARY
          fi

      - name: Update commit status
        uses: actions/github-script@v7
        if: always()
        with:
          script: |
            const state = '${{ job.status }}' === 'success' ? 'success' : 'failure';
            const description = state === 'success' 
              ? 'All security and quality gates passed' 
              : 'Security or quality issues found';
            
            github.rest.repos.createCommitStatus({
              owner: context.repo.owner,
              repo: context.repo.repo,
              sha: context.sha,
              state: state,
              target_url: `${context.serverUrl}/${context.repo.owner}/${context.repo.repo}/actions/runs/${context.runId}`,
              description: description,
              context: 'Security and Quality Gates'
            });

  # Generate security report
  security-report:
    name: Generate Security Report
    runs-on: ubuntu-latest
    needs: [dependency-scan, code-analysis, secret-scan, container-scan]
    if: always()
    steps:
      - name: Download all scan results
        uses: actions/download-artifact@v4
        with:
          path: scan-results/

      - name: Generate comprehensive security report
        run: |
          mkdir -p reports
          
          cat > reports/security-report.md << 'EOF'
          # LegacyBridge Security Report
          
          **Generated**: $(date -u)
          **Commit**: ${{ github.sha }}
          **Branch**: ${{ github.ref_name }}
          
          ## Summary
          
          | Scan Type | Status | Details |
          |-----------|--------|---------|
          | Dependency Scan | ${{ needs.dependency-scan.result }} | Vulnerabilities: ${{ needs.dependency-scan.outputs.vulnerabilities-found || 'N/A' }} |
          | Code Analysis | ${{ needs.code-analysis.result }} | Static analysis completed |
          | Secret Scan | ${{ needs.secret-scan.result }} | No secrets detected |
          | Container Scan | ${{ needs.container-scan.result }} | Container security verified |
          
          ## Recommendations
          
          - Keep dependencies updated regularly
          - Run security scans on every PR
          - Monitor for new vulnerabilities daily
          - Review and rotate secrets quarterly
          
          ## Next Steps
          
          - [ ] Address any failed security checks
          - [ ] Update security documentation
          - [ ] Schedule next security review
          
          EOF

      - name: Upload security report
        uses: actions/upload-artifact@v4
        with:
          name: security-report
          path: reports/security-report.md
          retention-days: 90
