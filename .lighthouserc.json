{"ci": {"collect": {"url": ["http://localhost:3000", "http://localhost:3000/api/health", "http://localhost:3000/api/status"], "startServerCommand": "npm start", "startServerReadyPattern": "ready on", "startServerReadyTimeout": 30000, "numberOfRuns": 3, "settings": {"chromeFlags": "--no-sandbox --headless --disable-gpu", "preset": "desktop", "throttling": {"rttMs": 40, "throughputKbps": 10240, "cpuSlowdownMultiplier": 1, "requestLatencyMs": 0, "downloadThroughputKbps": 0, "uploadThroughputKbps": 0}, "emulatedFormFactor": "desktop", "locale": "en-US"}}, "assert": {"assertions": {"categories:performance": ["error", {"minScore": 0.85}], "categories:accessibility": ["error", {"minScore": 0.95}], "categories:best-practices": ["error", {"minScore": 0.9}], "categories:seo": ["error", {"minScore": 0.85}], "categories:pwa": ["warn", {"minScore": 0.7}]}}, "upload": {"target": "temporary-public-storage"}, "server": {"port": 9001, "storage": {"storageMethod": "filesystem", "storagePath": ".lighthouseci"}}}}