// Unified Error Handling System
// Consolidates all error handling approaches into a single, consistent system

use thiserror::Error;
use serde::{Serialize, Deserialize};
use std::fmt;
use std::sync::atomic::{AtomicU64, Ordering};
use uuid::Uuid;

/// Global error counter for generating unique error IDs
static ERROR_COUNTER: AtomicU64 = AtomicU64::new(1);

/// Generate a unique error ID for tracking
pub fn generate_error_id() -> String {
    let counter = ERROR_COUNTER.fetch_add(1, Ordering::Relaxed);
    format!("ERR-{:08X}-{}", counter, Uuid::new_v4().simple().to_string()[..8].to_uppercase())
}

/// Unified error type that works across all boundaries (FFI, Tauri, internal)
#[derive(Error, Debug, <PERSON>lone, Serialize, Deserialize)]
#[serde(tag = "type", content = "details")]
pub enum UnifiedError {
    /// Input validation errors
    #[error("Invalid input: {message}")]
    InvalidInput {
        message: String,
        field: Option<String>,
        expected: Option<String>,
        received: Option<String>,
        error_id: String,
    },

    /// Conversion/processing errors
    #[error("Conversion failed: {message}")]
    ConversionFailed {
        message: String,
        source_format: Option<String>,
        target_format: Option<String>,
        line: Option<u32>,
        column: Option<u32>,
        error_id: String,
    },

    /// IO and file system errors
    #[error("IO error: {message}")]
    IoError {
        message: String,
        operation: Option<String>,
        path: Option<String>,
        error_code: Option<i32>,
        error_id: String,
    },

    /// Resource and memory errors
    #[error("Resource limit exceeded: {message}")]
    ResourceLimit {
        message: String,
        resource_type: String,
        current: Option<usize>,
        limit: Option<usize>,
        error_id: String,
    },

    /// Security and validation errors
    #[error("Security violation: {message}")]
    SecurityViolation {
        message: String,
        violation_type: String,
        error_id: String,
    },

    /// Timeout errors
    #[error("Operation timed out: {message}")]
    Timeout {
        message: String,
        timeout_seconds: u64,
        error_id: String,
    },

    /// System and configuration errors
    #[error("System error: {message}")]
    SystemError {
        message: String,
        component: String,
        error_code: Option<u32>,
        error_id: String,
    },

    /// Internal errors (should not be exposed to users)
    #[error("Internal error occurred")]
    InternalError {
        error_id: String,
        // Internal details are not serialized for security
        #[serde(skip)]
        internal_message: Option<String>,
    },
}

impl UnifiedError {
    /// Create an invalid input error
    pub fn invalid_input(message: &str) -> Self {
        Self::InvalidInput {
            message: message.to_string(),
            field: None,
            expected: None,
            received: None,
            error_id: generate_error_id(),
        }
    }

    /// Create an invalid input error with field details
    pub fn invalid_input_field(field: &str, expected: &str, received: &str) -> Self {
        Self::InvalidInput {
            message: format!("Invalid value for field '{}'", field),
            field: Some(field.to_string()),
            expected: Some(expected.to_string()),
            received: Some(received.to_string()),
            error_id: generate_error_id(),
        }
    }

    /// Create a conversion error
    pub fn conversion_failed(message: &str) -> Self {
        Self::ConversionFailed {
            message: message.to_string(),
            source_format: None,
            target_format: None,
            line: None,
            column: None,
            error_id: generate_error_id(),
        }
    }

    /// Create a conversion error with format details
    pub fn conversion_failed_with_formats(
        message: &str,
        source_format: &str,
        target_format: &str,
    ) -> Self {
        Self::ConversionFailed {
            message: message.to_string(),
            source_format: Some(source_format.to_string()),
            target_format: Some(target_format.to_string()),
            line: None,
            column: None,
            error_id: generate_error_id(),
        }
    }

    /// Create an IO error
    pub fn io_error(message: &str, operation: Option<&str>, path: Option<&str>) -> Self {
        Self::IoError {
            message: message.to_string(),
            operation: operation.map(|s| s.to_string()),
            path: path.map(|s| s.to_string()),
            error_code: None,
            error_id: generate_error_id(),
        }
    }

    /// Create a resource limit error
    pub fn resource_limit(
        message: &str,
        resource_type: &str,
        current: Option<usize>,
        limit: Option<usize>,
    ) -> Self {
        Self::ResourceLimit {
            message: message.to_string(),
            resource_type: resource_type.to_string(),
            current,
            limit,
            error_id: generate_error_id(),
        }
    }

    /// Create a security violation error
    pub fn security_violation(message: &str, violation_type: &str) -> Self {
        Self::SecurityViolation {
            message: message.to_string(),
            violation_type: violation_type.to_string(),
            error_id: generate_error_id(),
        }
    }

    /// Create a timeout error
    pub fn timeout(message: &str, timeout_seconds: u64) -> Self {
        Self::Timeout {
            message: message.to_string(),
            timeout_seconds,
            error_id: generate_error_id(),
        }
    }

    /// Create a system error
    pub fn system_error(message: &str, component: &str) -> Self {
        Self::SystemError {
            message: message.to_string(),
            component: component.to_string(),
            error_code: None,
            error_id: generate_error_id(),
        }
    }

    /// Create an internal error (for development/logging only)
    pub fn internal_error(internal_message: &str) -> Self {
        Self::InternalError {
            error_id: generate_error_id(),
            internal_message: Some(internal_message.to_string()),
        }
    }

    /// Get the error ID for tracking
    pub fn error_id(&self) -> &str {
        match self {
            Self::InvalidInput { error_id, .. } => error_id,
            Self::ConversionFailed { error_id, .. } => error_id,
            Self::IoError { error_id, .. } => error_id,
            Self::ResourceLimit { error_id, .. } => error_id,
            Self::SecurityViolation { error_id, .. } => error_id,
            Self::Timeout { error_id, .. } => error_id,
            Self::SystemError { error_id, .. } => error_id,
            Self::InternalError { error_id, .. } => error_id,
        }
    }

    /// Convert to FFI error code
    pub fn to_ffi_code(&self) -> i32 {
        match self {
            Self::InvalidInput { .. } => -1,
            Self::ConversionFailed { .. } => -2,
            Self::IoError { .. } => -3,
            Self::ResourceLimit { .. } => -4,
            Self::SecurityViolation { .. } => -5,
            Self::Timeout { .. } => -6,
            Self::SystemError { .. } => -7,
            Self::InternalError { .. } => -99,
        }
    }

    /// Get user-safe message (sanitized for external consumption)
    pub fn user_message(&self) -> String {
        match self {
            Self::InvalidInput { message, .. } => message.clone(),
            Self::ConversionFailed { message, .. } => message.clone(),
            Self::IoError { message, .. } => message.clone(),
            Self::ResourceLimit { message, .. } => message.clone(),
            Self::SecurityViolation { message, .. } => message.clone(),
            Self::Timeout { message, .. } => message.clone(),
            Self::SystemError { message, .. } => message.clone(),
            Self::InternalError { .. } => "An internal error occurred. Please contact support.".to_string(),
        }
    }

    /// Get developer message (includes internal details for logging)
    pub fn developer_message(&self) -> String {
        match self {
            Self::InternalError { internal_message, error_id, .. } => {
                format!("Internal error [{}]: {}", 
                    error_id, 
                    internal_message.as_deref().unwrap_or("Unknown internal error")
                )
            }
            _ => self.to_string(),
        }
    }
}

/// Result type for unified error handling
pub type UnifiedResult<T> = Result<T, UnifiedError>;

/// Extension trait for converting other error types to UnifiedError
pub trait IntoUnifiedError<T> {
    fn into_unified(self) -> UnifiedResult<T>;
    fn into_unified_with_context(self, context: &str) -> UnifiedResult<T>;
}

impl<T> IntoUnifiedError<T> for Result<T, std::io::Error> {
    fn into_unified(self) -> UnifiedResult<T> {
        self.map_err(|e| UnifiedError::io_error(&e.to_string(), None, None))
    }

    fn into_unified_with_context(self, context: &str) -> UnifiedResult<T> {
        self.map_err(|e| UnifiedError::io_error(&format!("{}: {}", context, e), Some(context), None))
    }
}

impl<T> IntoUnifiedError<T> for Result<T, std::str::Utf8Error> {
    fn into_unified(self) -> UnifiedResult<T> {
        self.map_err(|e| UnifiedError::invalid_input(&format!("Invalid UTF-8: {}", e)))
    }

    fn into_unified_with_context(self, context: &str) -> UnifiedResult<T> {
        self.map_err(|e| UnifiedError::invalid_input(&format!("{}: Invalid UTF-8: {}", context, e)))
    }
}

/// Macro for easy error creation
#[macro_export]
macro_rules! unified_error {
    (invalid_input, $msg:expr) => {
        UnifiedError::invalid_input($msg)
    };
    (conversion_failed, $msg:expr) => {
        UnifiedError::conversion_failed($msg)
    };
    (io_error, $msg:expr) => {
        UnifiedError::io_error($msg, None, None)
    };
    (resource_limit, $msg:expr, $resource:expr) => {
        UnifiedError::resource_limit($msg, $resource, None, None)
    };
    (security_violation, $msg:expr, $type:expr) => {
        UnifiedError::security_violation($msg, $type)
    };
    (timeout, $msg:expr, $seconds:expr) => {
        UnifiedError::timeout($msg, $seconds)
    };
    (system_error, $msg:expr, $component:expr) => {
        UnifiedError::system_error($msg, $component)
    };
    (internal_error, $msg:expr) => {
        UnifiedError::internal_error($msg)
    };
}
