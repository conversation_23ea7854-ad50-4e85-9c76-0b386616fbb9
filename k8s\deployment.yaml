apiVersion: apps/v1
kind: Deployment
metadata:
  name: legacybridge
  namespace: legacybridge
  labels:
    app: legacybridge
    app.kubernetes.io/name: legacybridge
    app.kubernetes.io/component: mcp-server
    app.kubernetes.io/part-of: legacybridge
    app.kubernetes.io/managed-by: kubernetes
    version: v1
  annotations:
    deployment.kubernetes.io/revision: "1"
    description: "LegacyBridge MCP Server - Production Deployment"
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  selector:
    matchLabels:
      app: legacybridge
      app.kubernetes.io/name: legacybridge
  template:
    metadata:
      labels:
        app: legacybridge
        app.kubernetes.io/name: legacybridge
        app.kubernetes.io/component: mcp-server
        app.kubernetes.io/part-of: legacybridge
        version: v1
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9090"
        prometheus.io/path: "/metrics"
        config.linkerd.io/proxy-cpu-request: "0.1"
        config.linkerd.io/proxy-memory-request: "20Mi"
        cluster-autoscaler.kubernetes.io/safe-to-evict: "true"
    spec:
      securityContext:
        runAsNonRoot: true
        runAsUser: 1001
        runAsGroup: 1001
        fsGroup: 1001
        seccompProfile:
          type: RuntimeDefault
      serviceAccountName: legacybridge
      terminationGracePeriodSeconds: 30
      containers:
      - name: legacybridge
        image: ghcr.io/legacybridge/legacybridge:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 3030
          name: http
          protocol: TCP
        - containerPort: 9090
          name: metrics
          protocol: TCP
        env:
        - name: NODE_ENV
          value: "production"
        - name: MCP_PORT
          value: "3030"
        - name: LOG_LEVEL
          value: "info"
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        envFrom:
        - configMapRef:
            name: legacybridge-config
        - secretRef:
            name: legacybridge-secrets
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
            ephemeral-storage: "1Gi"
          limits:
            memory: "1Gi"
            cpu: "2000m"
            ephemeral-storage: "2Gi"
        livenessProbe:
          httpGet:
            path: /health
            port: http
            scheme: HTTP
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
          successThreshold: 1
        readinessProbe:
          httpGet:
            path: /health
            port: http
            scheme: HTTP
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
          successThreshold: 1
        startupProbe:
          httpGet:
            path: /health
            port: http
            scheme: HTTP
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 30
          successThreshold: 1
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          runAsNonRoot: true
          runAsUser: 1001
          runAsGroup: 1001
          capabilities:
            drop:
            - ALL
          seccompProfile:
            type: RuntimeDefault
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: app-cache
          mountPath: /app/cache
        - name: app-logs
          mountPath: /app/logs
        - name: app-temp
          mountPath: /app/temp
      volumes:
      - name: tmp
        emptyDir:
          sizeLimit: 1Gi
      - name: app-cache
        emptyDir:
          sizeLimit: 2Gi
      - name: app-logs
        emptyDir:
          sizeLimit: 1Gi
      - name: app-temp
        emptyDir:
          sizeLimit: 1Gi
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: app.kubernetes.io/name
                operator: In
                values:
                - legacybridge
            topologyKey: kubernetes.io/hostname
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            preference:
              matchExpressions:
              - key: node.kubernetes.io/instance-type
                operator: In
                values:
                - m5.large
                - m5.xlarge
                - c5.large
                - c5.xlarge
      tolerations:
      - key: "node.kubernetes.io/not-ready"
        operator: "Exists"
        effect: "NoExecute"
        tolerationSeconds: 300
      - key: "node.kubernetes.io/unreachable"
        operator: "Exists"
        effect: "NoExecute"
        tolerationSeconds: 300
---
apiVersion: v1
kind: Service
metadata:
  name: legacybridge
  namespace: legacybridge
  labels:
    app: legacybridge
    app.kubernetes.io/name: legacybridge
    app.kubernetes.io/component: mcp-server
    app.kubernetes.io/part-of: legacybridge
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: "http"
    prometheus.io/scrape: "true"
    prometheus.io/port: "9090"
    prometheus.io/path: "/metrics"
spec:
  type: ClusterIP
  sessionAffinity: None
  ports:
  - port: 80
    targetPort: 3030
    protocol: TCP
    name: http
  - port: 9090
    targetPort: 9090
    protocol: TCP
    name: metrics
  selector:
    app: legacybridge
    app.kubernetes.io/name: legacybridge
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: legacybridge
  namespace: legacybridge
  labels:
    app: legacybridge
    app.kubernetes.io/name: legacybridge
    app.kubernetes.io/component: mcp-server
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: legacybridge
      app.kubernetes.io/name: legacybridge
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: legacybridge
  namespace: legacybridge
  labels:
    app: legacybridge
    app.kubernetes.io/name: legacybridge
    app.kubernetes.io/component: mcp-server
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: legacybridge
  minReplicas: 3
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  - type: Pods
    pods:
      metric:
        name: http_requests_per_second
      target:
        type: AverageValue
        averageValue: "100"
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
      - type: Pods
        value: 2
        periodSeconds: 60
      selectPolicy: Min
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 60
      - type: Pods
        value: 4
        periodSeconds: 60
      selectPolicy: Max