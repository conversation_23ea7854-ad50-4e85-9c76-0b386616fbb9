{"config": {"configFile": "C:\\dev\\legacy-bridge\\legacybridge\\playwright.config.ts", "rootDir": "C:/dev/legacy-bridge/legacybridge/tests", "forbidOnly": false, "fullyParallel": true, "globalSetup": "C:\\dev\\legacy-bridge\\legacybridge\\tests\\setup\\global-setup.ts", "globalTeardown": "C:\\dev\\legacy-bridge\\legacybridge\\tests\\setup\\global-teardown.ts", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "tests/reports/playwright-report"}], ["json", {"outputFile": "tests/reports/playwright-results.json"}], ["junit", {"outputFile": "tests/reports/playwright-junit.xml"}], ["list", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/dev/legacy-bridge/legacybridge/tests/results", "repeatEach": 1, "retries": 1, "metadata": {}, "id": "chromium", "name": "chromium", "testDir": "C:/dev/legacy-bridge/legacybridge/tests", "testIgnore": [], "testMatch": ["**/e2e/**/*.spec.ts", "**/accessibility/**/*.spec.ts", "**/visual-regression/**/*.spec.ts", "**/chaos/**/*.spec.ts"], "timeout": 60000}, {"outputDir": "C:/dev/legacy-bridge/legacybridge/tests/results", "repeatEach": 1, "retries": 1, "metadata": {}, "id": "firefox", "name": "firefox", "testDir": "C:/dev/legacy-bridge/legacybridge/tests", "testIgnore": [], "testMatch": ["**/e2e/**/*.spec.ts", "**/accessibility/**/*.spec.ts", "**/visual-regression/**/*.spec.ts", "**/chaos/**/*.spec.ts"], "timeout": 60000}, {"outputDir": "C:/dev/legacy-bridge/legacybridge/tests/results", "repeatEach": 1, "retries": 1, "metadata": {}, "id": "webkit", "name": "webkit", "testDir": "C:/dev/legacy-bridge/legacybridge/tests", "testIgnore": [], "testMatch": ["**/e2e/**/*.spec.ts", "**/accessibility/**/*.spec.ts", "**/visual-regression/**/*.spec.ts", "**/chaos/**/*.spec.ts"], "timeout": 60000}, {"outputDir": "C:/dev/legacy-bridge/legacybridge/tests/results", "repeatEach": 1, "retries": 1, "metadata": {}, "id": "mobile-chrome", "name": "mobile-chrome", "testDir": "C:/dev/legacy-bridge/legacybridge/tests", "testIgnore": [], "testMatch": ["**/e2e/**/*.spec.ts", "**/accessibility/**/*.spec.ts", "**/visual-regression/**/*.spec.ts", "**/chaos/**/*.spec.ts"], "timeout": 60000}, {"outputDir": "C:/dev/legacy-bridge/legacybridge/tests/results", "repeatEach": 1, "retries": 1, "metadata": {}, "id": "mobile-safari", "name": "mobile-safari", "testDir": "C:/dev/legacy-bridge/legacybridge/tests", "testIgnore": [], "testMatch": ["**/e2e/**/*.spec.ts", "**/accessibility/**/*.spec.ts", "**/visual-regression/**/*.spec.ts", "**/chaos/**/*.spec.ts"], "timeout": 60000}, {"outputDir": "C:/dev/legacy-bridge/legacybridge/tests/results", "repeatEach": 1, "retries": 1, "metadata": {}, "id": "tablet", "name": "tablet", "testDir": "C:/dev/legacy-bridge/legacybridge/tests", "testIgnore": [], "testMatch": ["**/e2e/**/*.spec.ts", "**/accessibility/**/*.spec.ts", "**/visual-regression/**/*.spec.ts", "**/chaos/**/*.spec.ts"], "timeout": 60000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.54.1", "workers": 6, "webServer": {"command": "npm run dev", "port": 3000, "timeout": 120000, "reuseExistingServer": true}}, "suites": [], "errors": [{"message": "Error: Cannot find module '@argos-ci/playwright'\nRequire stack:\n- C:\\dev\\legacy-bridge\\legacybridge\\tests\\visual-regression\\visual-regression-tests.spec.ts\n- C:\\dev\\legacy-bridge\\legacybridge\\node_modules\\playwright\\lib\\transform\\transform.js\n- C:\\dev\\legacy-bridge\\legacybridge\\node_modules\\playwright\\lib\\common\\configLoader.js\n- C:\\dev\\legacy-bridge\\legacybridge\\node_modules\\playwright\\lib\\program.js\n- C:\\dev\\legacy-bridge\\legacybridge\\node_modules\\@playwright\\test\\cli.js", "stack": "Error: Cannot find module '@argos-ci/playwright'\nRequire stack:\n- C:\\dev\\legacy-bridge\\legacybridge\\tests\\visual-regression\\visual-regression-tests.spec.ts\n- C:\\dev\\legacy-bridge\\legacybridge\\node_modules\\playwright\\lib\\transform\\transform.js\n- C:\\dev\\legacy-bridge\\legacybridge\\node_modules\\playwright\\lib\\common\\configLoader.js\n- C:\\dev\\legacy-bridge\\legacybridge\\node_modules\\playwright\\lib\\program.js\n- C:\\dev\\legacy-bridge\\legacybridge\\node_modules\\@playwright\\test\\cli.js\n    at Object.<anonymous> (C:\\dev\\legacy-bridge\\legacybridge\\tests\\visual-regression\\visual-regression-tests.spec.ts:2:1)", "location": {"file": "C:\\dev\\legacy-bridge\\legacybridge\\tests\\visual-regression\\visual-regression-tests.spec.ts", "column": 1, "line": 2}, "snippet": "\u001b[90m   at \u001b[39mvisual-regression\\visual-regression-tests.spec.ts:2\n\n\u001b[0m \u001b[90m 1 |\u001b[39m \u001b[36mimport\u001b[39m { test\u001b[33m,\u001b[39m expect } \u001b[36mfrom\u001b[39m \u001b[32m'@playwright/test'\u001b[39m\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 2 |\u001b[39m \u001b[36mimport\u001b[39m { argosScreenshot } \u001b[36mfrom\u001b[39m \u001b[32m'@argos-ci/playwright'\u001b[39m\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 3 |\u001b[39m \u001b[36mimport\u001b[39m pixelmatch \u001b[36mfrom\u001b[39m \u001b[32m'pixelmatch'\u001b[39m\u001b[33m;\u001b[39m\n \u001b[90m 4 |\u001b[39m \u001b[36mimport\u001b[39m { \u001b[33mPNG\u001b[39m } \u001b[36mfrom\u001b[39m \u001b[32m'pngjs'\u001b[39m\u001b[33m;\u001b[39m\n \u001b[90m 5 |\u001b[39m \u001b[36mimport\u001b[39m fs \u001b[36mfrom\u001b[39m \u001b[32m'fs'\u001b[39m\u001b[33m;\u001b[39m\u001b[0m"}], "stats": {"startTime": "2025-07-31T00:41:56.284Z", "duration": 45810821.084, "expected": 0, "skipped": 0, "unexpected": 0, "flaky": 0}}