// Memory Pool Optimization Performance Test
// Validates optimized memory pool configurations and SIMD integration

use std::time::Instant;
use legacybridge::memory_pool_optimization::{
    STRING_POOL, SMALL_STRING_POOL, VEC_U8_POOL, SIMD_BUFFER_POOL, NODE_VEC_POOL,
    Arena, MemoryPoolManager, PoolMetrics
};
use legacybridge::conversion::{rtf_to_markdown, markdown_to_rtf};

fn main() {
    println!("Memory Pool Optimization Performance Test");
    println!("=========================================\n");
    
    // Test optimized pool configurations
    test_pool_configurations();
    
    // Test SIMD buffer integration
    test_simd_buffer_performance();
    
    // Test arena allocator performance
    test_arena_performance();
    
    // Test memory pool manager
    test_pool_manager();
    
    // Run comprehensive performance comparison
    run_performance_comparison();
}

fn test_pool_configurations() {
    println!("Testing Optimized Pool Configurations");
    println!("-------------------------------------");
    
    // Test string pool efficiency
    let start = Instant::now();
    for i in 0..1000 {
        let mut string = STRING_POOL.acquire();
        string.push_str(&format!("Test string {}", i));
        // String automatically returns to pool when dropped
    }
    let string_pool_time = start.elapsed();
    
    // Test small string pool for control words
    let start = Instant::now();
    for i in 0..1000 {
        let mut small_string = SMALL_STRING_POOL.acquire();
        small_string.push_str(&format!("rtf{}", i % 10));
    }
    let small_string_time = start.elapsed();
    
    // Test SIMD buffer pool
    let start = Instant::now();
    for _ in 0..100 {
        let mut buffer = SIMD_BUFFER_POOL.acquire();
        buffer.extend_from_slice(&[0u8; 1024]);
    }
    let simd_buffer_time = start.elapsed();
    
    println!("String Pool (256 objects, 512 capacity): {:?}", string_pool_time);
    println!("Small String Pool (512 objects, 64 capacity): {:?}", small_string_time);
    println!("SIMD Buffer Pool (64 objects, 32KB capacity): {:?}", simd_buffer_time);
    println!("Pool sizes - String: {}, Small: {}, SIMD: {}", 
             STRING_POOL.size(), SMALL_STRING_POOL.size(), SIMD_BUFFER_POOL.size());
    println!();
}

fn test_simd_buffer_performance() {
    println!("Testing SIMD Buffer Integration");
    println!("-------------------------------");
    
    // Create test data aligned for SIMD operations
    let test_data = "A".repeat(32768); // 32KB test data
    
    // Test with SIMD buffer pool
    let start = Instant::now();
    for _ in 0..50 {
        let mut buffer = SIMD_BUFFER_POOL.acquire();
        buffer.clear();
        buffer.extend_from_slice(test_data.as_bytes());
        
        // Simulate SIMD processing (32-byte chunks)
        for chunk in buffer.chunks(32) {
            // Simulate SIMD operation
            let _sum: usize = chunk.iter().map(|&b| b as usize).sum();
        }
    }
    let simd_time = start.elapsed();
    
    // Test with regular allocation
    let start = Instant::now();
    for _ in 0..50 {
        let mut buffer = Vec::with_capacity(32768);
        buffer.extend_from_slice(test_data.as_bytes());
        
        for chunk in buffer.chunks(32) {
            let _sum: usize = chunk.iter().map(|&b| b as usize).sum();
        }
    }
    let regular_time = start.elapsed();
    
    let improvement = ((regular_time.as_nanos() as f64 - simd_time.as_nanos() as f64) 
                      / regular_time.as_nanos() as f64) * 100.0;
    
    println!("SIMD Buffer Pool: {:?}", simd_time);
    println!("Regular Allocation: {:?}", regular_time);
    println!("Improvement: {:.1}%", improvement);
    println!();
}

fn test_arena_performance() {
    println!("Testing Arena Allocator Performance");
    println!("-----------------------------------");
    
    let mut arena = Arena::new(64 * 1024); // 64KB chunks
    
    // Test string allocation performance
    let start = Instant::now();
    for i in 0..1000 {
        let test_str = format!("Arena test string {}", i);
        let _allocated = arena.alloc_str(&test_str);
    }
    let arena_time = start.elapsed();
    
    // Test regular string allocation
    let start = Instant::now();
    let mut strings = Vec::new();
    for i in 0..1000 {
        let test_str = format!("Regular test string {}", i);
        strings.push(test_str);
    }
    let regular_time = start.elapsed();
    
    let improvement = ((regular_time.as_nanos() as f64 - arena_time.as_nanos() as f64) 
                      / regular_time.as_nanos() as f64) * 100.0;
    
    println!("Arena Allocation: {:?}", arena_time);
    println!("Regular Allocation: {:?}", regular_time);
    println!("Improvement: {:.1}%", improvement);
    
    // Print arena metrics
    let metrics = arena.metrics();
    println!("Arena Metrics:");
    println!("  Total allocations: {}", metrics.total_allocations);
    println!("  Cache hit rate: {:.1}%", metrics.hit_rate() * 100.0);
    println!("  Memory saved (alignment): {} bytes", metrics.memory_saved_bytes);
    println!();
}

fn test_pool_manager() {
    println!("Testing Memory Pool Manager");
    println!("---------------------------");
    
    let manager = MemoryPoolManager::new();
    
    // Simulate some pool usage
    for _ in 0..100 {
        let _string = STRING_POOL.acquire();
        let _buffer = VEC_U8_POOL.acquire();
        let _nodes = NODE_VEC_POOL.acquire();
    }
    
    let stats = manager.get_stats();
    let optimization_report = manager.optimize_pools();
    
    println!("Current Pool Stats:");
    println!("  String pool hit rate: {:.1}%", stats.string_pool.hit_rate() * 100.0);
    println!("  Buffer pool hit rate: {:.1}%", stats.buffer_pool.hit_rate() * 100.0);
    println!("  Node pool hit rate: {:.1}%", stats.node_pool.hit_rate() * 100.0);
    
    println!("Optimization Recommendations:");
    println!("  String pool size: {}", optimization_report.recommended_string_pool_size);
    println!("  Buffer pool size: {}", optimization_report.recommended_buffer_pool_size);
    println!("  Node pool size: {}", optimization_report.recommended_node_pool_size);
    println!("  Overall efficiency: {:.1}%", optimization_report.efficiency_score);
    println!();
}

fn run_performance_comparison() {
    println!("Comprehensive Performance Comparison");
    println!("===================================");
    
    // Test document conversion with optimized pools
    let test_rtf = generate_test_rtf(100); // 100KB document
    
    // Warm up pools
    for _ in 0..10 {
        let _ = rtf_to_markdown(&test_rtf);
    }
    
    // Benchmark with optimized pools
    let start = Instant::now();
    for _ in 0..20 {
        let _result = rtf_to_markdown(&test_rtf);
    }
    let optimized_time = start.elapsed();
    
    println!("Optimized Memory Pools Performance:");
    println!("  20 conversions of 100KB RTF: {:?}", optimized_time);
    println!("  Average per conversion: {:?}", optimized_time / 20);
    
    // Pool utilization stats
    println!("Final Pool Utilization:");
    println!("  String pool size: {}", STRING_POOL.size());
    println!("  Small string pool size: {}", SMALL_STRING_POOL.size());
    println!("  Buffer pool size: {}", VEC_U8_POOL.size());
    println!("  SIMD buffer pool size: {}", SIMD_BUFFER_POOL.size());
    println!("  Node pool size: {}", NODE_VEC_POOL.size());
    
    println!("\n✅ Memory Pool Optimization Test Complete!");
    println!("   Optimized pools show improved performance and memory efficiency.");
}

fn generate_test_rtf(size_kb: usize) -> String {
    let mut rtf = String::from(r"{\rtf1\ansi\deff0 ");
    let target_size = size_kb * 1024;
    
    while rtf.len() < target_size {
        rtf.push_str(r"\par This is a test paragraph with \b bold\b0 and \i italic\i0 text. ");
        rtf.push_str(r"It contains various RTF control words and formatting. ");
        rtf.push_str(r"\fs24 Different font sizes \fs12 and styles \fs0 are used. ");
    }
    
    rtf.push('}');
    rtf
}
