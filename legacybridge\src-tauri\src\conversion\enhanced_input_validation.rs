// Enhanced Input Validation System
// Provides comprehensive security-focused input validation for all content types

use std::collections::HashSet;
use regex::Regex;
use crate::conversion::unified_error_system::{UnifiedError, UnifiedResult};

/// Enhanced security limits with more granular controls
#[derive(Debug, Clone)]
pub struct EnhancedSecurityLimits {
    /// Maximum file size in bytes (default: 10MB)
    pub max_file_size: usize,
    /// Maximum nesting depth for RTF groups (default: 100)
    pub max_nesting_depth: usize,
    /// Maximum number of control words (default: 10,000)
    pub max_control_words: usize,
    /// Maximum table dimensions (default: 1000x1000)
    pub max_table_rows: usize,
    pub max_table_cols: usize,
    /// Maximum line length (default: 10,000 chars)
    pub max_line_length: usize,
    /// Maximum number of links in markdown (default: 100)
    pub max_links: usize,
    /// Maximum image size references (default: 100MB)
    pub max_image_size_ref: usize,
}

impl Default for EnhancedSecurityLimits {
    fn default() -> Self {
        Self {
            max_file_size: 10 * 1024 * 1024, // 10MB
            max_nesting_depth: 100,
            max_control_words: 10_000,
            max_table_rows: 1_000,
            max_table_cols: 1_000,
            max_line_length: 10_000,
            max_links: 100,
            max_image_size_ref: 100 * 1024 * 1024, // 100MB
        }
    }
}

/// Enhanced input validator with comprehensive security checks
pub struct EnhancedInputValidator {
    limits: EnhancedSecurityLimits,
    dangerous_rtf_patterns: HashSet<String>,
    dangerous_markdown_patterns: Vec<Regex>,
    allowed_protocols: HashSet<String>,
}

impl Default for EnhancedInputValidator {
    fn default() -> Self {
        Self::new()
    }
}

impl EnhancedInputValidator {
    /// Create a new enhanced input validator
    pub fn new() -> Self {
        let mut dangerous_rtf_patterns = HashSet::new();
        
        // RTF control words that can be dangerous
        dangerous_rtf_patterns.insert(r"\object".to_string());
        dangerous_rtf_patterns.insert(r"\objdata".to_string());
        dangerous_rtf_patterns.insert(r"\objemb".to_string());
        dangerous_rtf_patterns.insert(r"\objlink".to_string());
        dangerous_rtf_patterns.insert(r"\result".to_string());
        dangerous_rtf_patterns.insert(r"\pict".to_string());
        dangerous_rtf_patterns.insert(r"\field".to_string());
        dangerous_rtf_patterns.insert(r"\fldinst".to_string());
        dangerous_rtf_patterns.insert(r"\fldrslt".to_string());
        dangerous_rtf_patterns.insert(r"\datafield".to_string());
        dangerous_rtf_patterns.insert(r"\*\generator".to_string());
        dangerous_rtf_patterns.insert(r"\*\mhtmltag".to_string());
        dangerous_rtf_patterns.insert(r"\stylesheet".to_string());
        dangerous_rtf_patterns.insert(r"\info".to_string());
        dangerous_rtf_patterns.insert(r"\author".to_string());
        dangerous_rtf_patterns.insert(r"\operator".to_string());
        dangerous_rtf_patterns.insert(r"\company".to_string());
        dangerous_rtf_patterns.insert(r"\doccomm".to_string());
        dangerous_rtf_patterns.insert(r"\*\xmlnstbl".to_string());
        dangerous_rtf_patterns.insert(r"\*\listtable".to_string());
        dangerous_rtf_patterns.insert(r"\*\revtbl".to_string());
        
        let dangerous_markdown_patterns = vec![
            // Script injection patterns
            Regex::new(r"(?i)<script[^>]*>").unwrap(),
            Regex::new(r"(?i)javascript:").unwrap(),
            Regex::new(r"(?i)vbscript:").unwrap(),
            Regex::new(r"(?i)data:text/html").unwrap(),
            Regex::new(r"(?i)data:application/").unwrap(),
            
            // Event handler patterns
            Regex::new(r"(?i)on\w+\s*=").unwrap(),
            
            // Dangerous HTML tags
            Regex::new(r"(?i)<(iframe|embed|object|applet|form|input|textarea|select|button)[^>]*>").unwrap(),
            
            // Meta refresh and redirects
            Regex::new(r"(?i)<meta[^>]*http-equiv[^>]*refresh").unwrap(),
            
            // External resource loading
            Regex::new(r"(?i)<link[^>]*href[^>]*//").unwrap(),
            
            // Suspicious protocols
            Regex::new(r"(?i)(file|ftp|gopher|ldap|dict|finger|telnet|ssh)://").unwrap(),
        ];
        
        let mut allowed_protocols = HashSet::new();
        allowed_protocols.insert("http".to_string());
        allowed_protocols.insert("https".to_string());
        allowed_protocols.insert("mailto".to_string());
        
        Self {
            limits: EnhancedSecurityLimits::default(),
            dangerous_rtf_patterns,
            dangerous_markdown_patterns,
            allowed_protocols,
        }
    }
    
    /// Create validator with custom limits
    pub fn with_limits(limits: EnhancedSecurityLimits) -> Self {
        let mut validator = Self::new();
        validator.limits = limits;
        validator
    }
    
    /// Comprehensive RTF validation
    pub fn validate_rtf_content(&self, content: &str) -> UnifiedResult<()> {
        // Basic size and structure validation
        self.validate_basic_structure(content, "RTF")?;
        
        // RTF-specific structure validation
        self.validate_rtf_structure(content)?;
        
        // Security validation
        self.validate_rtf_security(content)?;
        
        // Performance validation
        self.validate_rtf_performance(content)?;
        
        Ok(())
    }
    
    /// Comprehensive Markdown validation
    pub fn validate_markdown_content(&self, content: &str) -> UnifiedResult<()> {
        // Basic size and structure validation
        self.validate_basic_structure(content, "Markdown")?;
        
        // Markdown-specific security validation
        self.validate_markdown_security(content)?;
        
        // Performance validation
        self.validate_markdown_performance(content)?;
        
        Ok(())
    }
    
    /// Basic structure validation for any content type
    fn validate_basic_structure(&self, content: &str, content_type: &str) -> UnifiedResult<()> {
        // Size validation
        if content.is_empty() {
            return Err(UnifiedError::invalid_input(&format!("{} content is empty", content_type)));
        }
        
        if content.len() > self.limits.max_file_size {
            return Err(UnifiedError::resource_limit(
                &format!("{} content exceeds maximum size", content_type),
                "file_size",
                Some(content.len()),
                Some(self.limits.max_file_size),
            ));
        }
        
        // Line length validation
        for (line_num, line) in content.lines().enumerate() {
            if line.len() > self.limits.max_line_length {
                return Err(UnifiedError::invalid_input(&format!(
                    "{} line {} exceeds maximum length of {} characters",
                    content_type, line_num + 1, self.limits.max_line_length
                )));
            }
        }
        
        // Check for null bytes (potential binary content)
        if content.contains('\0') {
            return Err(UnifiedError::security_violation(
                &format!("{} content contains null bytes (potential binary data)", content_type),
                "binary_content"
            ));
        }
        
        // Check for excessive control characters
        let control_char_count = content.chars().filter(|c| c.is_control() && *c != '\n' && *c != '\r' && *c != '\t').count();
        if control_char_count > content.len() / 10 {
            return Err(UnifiedError::security_violation(
                &format!("{} content has excessive control characters", content_type),
                "control_characters"
            ));
        }
        
        Ok(())
    }
    
    /// RTF-specific structure validation
    fn validate_rtf_structure(&self, content: &str) -> UnifiedResult<()> {
        let trimmed = content.trim();
        
        // Must start with {\rtf
        if !trimmed.starts_with(r"{\rtf") {
            return Err(UnifiedError::invalid_input("RTF content must start with {\\rtf"));
        }
        
        // Must end with }
        if !trimmed.ends_with('}') {
            return Err(UnifiedError::invalid_input("RTF content must end with }"));
        }
        
        // Validate brace balance
        let mut brace_depth = 0;
        let mut max_depth = 0;
        
        for ch in content.chars() {
            match ch {
                '{' => {
                    brace_depth += 1;
                    max_depth = max_depth.max(brace_depth);
                    if max_depth > self.limits.max_nesting_depth {
                        return Err(UnifiedError::resource_limit(
                            "RTF nesting depth exceeds maximum",
                            "nesting_depth",
                            Some(max_depth),
                            Some(self.limits.max_nesting_depth),
                        ));
                    }
                }
                '}' => {
                    if brace_depth == 0 {
                        return Err(UnifiedError::invalid_input("RTF has unbalanced braces (extra closing brace)"));
                    }
                    brace_depth -= 1;
                }
                _ => {}
            }
        }
        
        if brace_depth != 0 {
            return Err(UnifiedError::invalid_input("RTF has unbalanced braces"));
        }
        
        // Count control words
        let control_word_count = content.matches('\\').count();
        if control_word_count > self.limits.max_control_words {
            return Err(UnifiedError::resource_limit(
                "RTF has too many control words",
                "control_words",
                Some(control_word_count),
                Some(self.limits.max_control_words),
            ));
        }
        
        Ok(())
    }
    
    /// RTF security validation
    fn validate_rtf_security(&self, content: &str) -> UnifiedResult<()> {
        // Check for dangerous control words
        for pattern in &self.dangerous_rtf_patterns {
            if content.contains(pattern) {
                return Err(UnifiedError::security_violation(
                    &format!("RTF contains forbidden control word: {}", pattern),
                    "forbidden_control_word"
                ));
            }
        }
        
        // Check for suspicious hex data patterns (potential embedded objects)
        if content.contains(r"\bin") && content.matches(r"\bin").count() > 5 {
            return Err(UnifiedError::security_violation(
                "RTF contains excessive binary data",
                "excessive_binary"
            ));
        }
        
        // Check for suspicious Unicode patterns
        if content.contains(r"\u") && content.matches(r"\u").count() > 1000 {
            return Err(UnifiedError::security_violation(
                "RTF contains excessive Unicode escapes",
                "excessive_unicode"
            ));
        }
        
        Ok(())
    }
    
    /// RTF performance validation
    fn validate_rtf_performance(&self, content: &str) -> UnifiedResult<()> {
        // Check for excessive table complexity
        let row_count = content.matches(r"\trowd").count();
        let cell_count = content.matches(r"\cellx").count();
        
        if row_count > self.limits.max_table_rows {
            return Err(UnifiedError::resource_limit(
                "RTF table has too many rows",
                "table_rows",
                Some(row_count),
                Some(self.limits.max_table_rows),
            ));
        }
        
        if cell_count > self.limits.max_table_cols * self.limits.max_table_rows {
            return Err(UnifiedError::resource_limit(
                "RTF table has too many cells",
                "table_cells",
                Some(cell_count),
                Some(self.limits.max_table_cols * self.limits.max_table_rows),
            ));
        }
        
        Ok(())
    }
    
    /// Markdown security validation
    fn validate_markdown_security(&self, content: &str) -> UnifiedResult<()> {
        // Check for dangerous patterns
        for pattern in &self.dangerous_markdown_patterns {
            if pattern.is_match(content) {
                return Err(UnifiedError::security_violation(
                    &format!("Markdown contains potentially malicious content: {}", pattern.as_str()),
                    "malicious_content"
                ));
            }
        }
        
        // Validate links
        let link_count = content.matches("](").count() + content.matches("](").count();
        if link_count > self.limits.max_links {
            return Err(UnifiedError::resource_limit(
                "Markdown has too many links",
                "link_count",
                Some(link_count),
                Some(self.limits.max_links),
            ));
        }
        
        // Check for data URLs with large payloads
        if content.contains("data:") {
            for line in content.lines() {
                if line.contains("data:") && line.len() > self.limits.max_image_size_ref {
                    return Err(UnifiedError::security_violation(
                        "Markdown contains data URL with excessive size",
                        "large_data_url"
                    ));
                }
            }
        }
        
        Ok(())
    }
    
    /// Markdown performance validation
    fn validate_markdown_performance(&self, content: &str) -> UnifiedResult<()> {
        // Check for excessive nesting (lists, quotes, etc.)
        let mut max_list_depth = 0;
        let mut current_depth = 0;
        
        for line in content.lines() {
            let trimmed = line.trim_start();
            if trimmed.starts_with("- ") || trimmed.starts_with("* ") || trimmed.starts_with("+ ") {
                let indent = line.len() - trimmed.len();
                current_depth = indent / 2; // Assuming 2-space indentation
                max_list_depth = max_list_depth.max(current_depth);
            }
        }
        
        if max_list_depth > 20 {
            return Err(UnifiedError::resource_limit(
                "Markdown has excessive list nesting",
                "list_nesting",
                Some(max_list_depth),
                Some(20),
            ));
        }
        
        Ok(())
    }
}
