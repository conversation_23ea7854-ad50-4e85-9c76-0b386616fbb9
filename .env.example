# LegacyBridge Environment Configuration Example
# Copy this file to .env and customize the values for your deployment

# =============================================================================
# CORE APPLICATION SETTINGS
# =============================================================================

# Environment mode (development, production, test)
NODE_ENV=production

# MCP Server port
MCP_PORT=3030

# Logging level (error, warn, info, debug)
LOG_LEVEL=info

# Application version (auto-populated during build)
VERSION=1.0.0

# =============================================================================
# SECURITY SETTINGS
# =============================================================================

# JWT secret for token signing (generate a secure random string)
JWT_SECRET=your-super-secure-jwt-secret-key-here

# Encryption key for sensitive data (32 characters recommended)
ENCRYPTION_KEY=your-32-character-encryption-key

# API keys for authentication (comma-separated)
API_KEYS=api-key-1,api-key-2,api-key-3

# Rate limiting settings
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100

# =============================================================================
# CACHING CONFIGURATION
# =============================================================================

# Enable caching (true/false)
CACHE_ENABLED=true

# Cache type (memory, redis)
CACHE_TYPE=memory

# Redis connection string (if using Redis cache)
REDIS_URL=redis://localhost:6379

# Cache TTL in seconds
CACHE_TTL=3600

# =============================================================================
# DATABASE SETTINGS
# =============================================================================

# PostgreSQL connection string
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/legacybridge

# Database pool settings
DB_POOL_MIN=2
DB_POOL_MAX=10

# =============================================================================
# LEGACY FORMAT SUPPORT
# =============================================================================

# Enable DOC format support (requires LibreOffice)
ENABLE_DOC=false

# Enable WordPerfect format support (requires LibreOffice)
ENABLE_WORDPERFECT=false

# LibreOffice path (if not in system PATH)
LIBREOFFICE_PATH=/usr/bin/libreoffice

# Pandoc path (if not in system PATH)
PANDOC_PATH=/usr/bin/pandoc

# =============================================================================
# FILE HANDLING
# =============================================================================

# Maximum file size for uploads (in bytes)
MAX_FILE_SIZE=10485760

# Upload directory
UPLOAD_DIR=./uploads

# Output directory
OUTPUT_DIR=./output

# Temporary directory
TEMP_DIR=./temp

# File cleanup interval (in milliseconds)
CLEANUP_INTERVAL=3600000

# =============================================================================
# MONITORING AND METRICS
# =============================================================================

# Enable metrics collection
METRICS_ENABLED=true

# Metrics port
METRICS_PORT=9090

# Enable dashboard
ENABLE_DASHBOARD=true

# Health check interval (in milliseconds)
HEALTH_CHECK_INTERVAL=30000

# =============================================================================
# EXTERNAL SERVICES
# =============================================================================

# MinIO/S3 configuration (for file storage)
S3_ENDPOINT=http://localhost:9000
S3_ACCESS_KEY=minioadmin
S3_SECRET_KEY=minioadmin
S3_BUCKET=legacybridge

# Email service configuration (for notifications)
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-password

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Enable debug mode
DEBUG=false

# Enable hot reload (development only)
HOT_RELOAD=false

# Enable source maps (development only)
SOURCE_MAPS=false

# =============================================================================
# DOCKER SPECIFIC SETTINGS
# =============================================================================

# Container timezone
TZ=UTC

# Container user ID (for volume permissions)
PUID=1001
PGID=1001

# =============================================================================
# SECURITY HEADERS
# =============================================================================

# Content Security Policy
CSP_POLICY="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'"

# CORS origins (comma-separated)
CORS_ORIGINS=http://localhost:3000,https://yourdomain.com

# =============================================================================
# PERFORMANCE TUNING
# =============================================================================

# Node.js memory limit (in MB)
NODE_MAX_OLD_SPACE_SIZE=512

# Worker thread count
WORKER_THREADS=4

# Request timeout (in milliseconds)
REQUEST_TIMEOUT=30000

# =============================================================================
# BACKUP AND RECOVERY
# =============================================================================

# Backup directory
BACKUP_DIR=./backups

# Backup retention days
BACKUP_RETENTION_DAYS=30

# Auto backup interval (in hours)
AUTO_BACKUP_INTERVAL=24
