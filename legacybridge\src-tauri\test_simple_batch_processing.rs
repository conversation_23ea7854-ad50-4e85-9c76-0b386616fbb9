// Simple Batch Processing Test
// Basic validation of batch processing functionality

use std::time::{Duration, Instant};
use uuid::Uuid;
use legacybridge::pipeline::enhanced_batch_processor::{
    EnhancedBatchProcessor, BatchProcessorConfig, BatchRequest, BatchPriority,
    BatchStatus
};
use legacybridge::pipeline::concurrent_processor::{ConversionRequest, ConversionContent, ConversionOptions};

#[tokio::main]
async fn main() {
    println!("Simple Batch Processing Test");
    println!("============================\n");
    
    // Test basic batch processor creation
    test_processor_creation().await;
    
    // Test single document batch
    println!("About to call test_single_document_batch()...");
    test_single_document_batch().await;
    println!("test_single_document_batch() completed.");
    
    // Test small batch
    test_small_batch().await;
    
    println!("\n✅ Simple Batch Processing Test Complete!");
}

async fn test_processor_creation() {
    println!("Testing Processor Creation");
    println!("--------------------------");
    
    let config = BatchProcessorConfig {
        max_concurrent: 4,
        max_batch_size: 10,
        document_timeout: Duration::from_secs(5),
        batch_timeout: Duration::from_secs(30),
        max_retries: 1,
        retry_delay: Duration::from_millis(100),
        progress_interval: Duration::from_millis(50),
        enable_metrics: true,
    };
    
    let processor = EnhancedBatchProcessor::new(config.clone());
    
    println!("✅ Processor created successfully");
    println!("  Max Concurrent: {}", config.max_concurrent);
    println!("  Max Batch Size: {}", config.max_batch_size);
    
    let stats = processor.get_processor_stats();
    println!("  Initial Stats: {} batches, {} documents", 
             stats.total_batches_processed, stats.total_documents_processed);
    println!();
}

async fn test_single_document_batch() {
    println!("Testing Single Document Batch");
    println!("-----------------------------");
    println!("Creating processor...");
    
    let processor = EnhancedBatchProcessor::new(BatchProcessorConfig::default());
    println!("Processor created. Creating document...");

    // Create single document
    let documents = vec![
        ConversionRequest {
            id: "single_doc".to_string(),
            content: ConversionContent::Memory(r"{\rtf1\ansi\deff0 {\fonttbl {\f0 Times New Roman;}} \f0\fs24 Single test document.}".to_string()),
            options: ConversionOptions::default(),
        }
    ];
    
    let batch_request = BatchRequest {
        batch_id: Uuid::new_v4().to_string(),
        documents,
        config: BatchProcessorConfig::default(),
        priority: BatchPriority::Normal,
        callback_url: None,
    };
    
    let start_time = Instant::now();
    println!("About to submit batch...");

    match processor.submit_batch(batch_request.clone()).await {
        Ok(mut progress_rx) => {
            println!("✅ Single document batch submitted: {}", batch_request.batch_id);
            
            // Monitor progress with timeout
            let timeout = Duration::from_secs(10);

            match tokio::time::timeout(timeout, async {
                while let Some(progress) = progress_rx.recv().await {
                    println!("  Progress: {:.1}% - Status: {:?}",
                             progress.progress_percentage, progress.status);

                    if progress.status == BatchStatus::Completed || progress.status == BatchStatus::Failed {
                        let elapsed = start_time.elapsed();
                        println!("  Single document batch completed in {:?}", elapsed);
                        println!("  Completed: {}, Failed: {}",
                                 progress.completed_documents, progress.failed_documents);
                        break;
                    }
                }
            }).await {
                Ok(_) => {
                    // Completed successfully
                }
                Err(_) => {
                    println!("  ⚠️ Single document batch timed out");
                }
            }
        }
        Err(e) => {
            println!("❌ Failed to submit single document batch: {:?}", e);
        }
    }
    
    println!();
}

async fn test_small_batch() {
    println!("Testing Small Batch (3 documents)");
    println!("----------------------------------");
    
    let processor = EnhancedBatchProcessor::new(BatchProcessorConfig::default());
    
    // Create small batch
    let documents = (0..3).map(|i| {
        ConversionRequest {
            id: format!("small_doc_{}", i),
            content: ConversionContent::Memory(format!(r"{{\rtf1\ansi\deff0 {{\fonttbl {{\f0 Times New Roman;}}}} \f0\fs24 Small batch document {} content.}}", i)),
            options: ConversionOptions::default(),
        }
    }).collect();
    
    let batch_request = BatchRequest {
        batch_id: Uuid::new_v4().to_string(),
        documents,
        config: BatchProcessorConfig::default(),
        priority: BatchPriority::Normal,
        callback_url: None,
    };
    
    let start_time = Instant::now();
    
    match processor.submit_batch(batch_request.clone()).await {
        Ok(mut progress_rx) => {
            println!("✅ Small batch submitted: {}", batch_request.batch_id);
            
            // Monitor progress with timeout
            let timeout = Duration::from_secs(15);

            match tokio::time::timeout(timeout, async {
                while let Some(progress) = progress_rx.recv().await {
                    println!("  Progress: {:.1}% ({}/{}) - Status: {:?}",
                             progress.progress_percentage,
                             progress.completed_documents + progress.failed_documents,
                             progress.total_documents,
                             progress.status);

                    if progress.status == BatchStatus::Completed || progress.status == BatchStatus::Failed {
                        let elapsed = start_time.elapsed();
                        println!("  Small batch completed in {:?}", elapsed);
                        println!("  Completed: {}, Failed: {}",
                                 progress.completed_documents, progress.failed_documents);
                        println!("  Throughput: {:.1} docs/sec", progress.throughput_docs_per_second);
                        break;
                    }
                }
            }).await {
                Ok(_) => {
                    // Completed successfully
                }
                Err(_) => {
                    println!("  ⚠️ Small batch timed out");
                }
            }
        }
        Err(e) => {
            println!("❌ Failed to submit small batch: {:?}", e);
        }
    }
    
    // Check final stats
    let stats = processor.get_processor_stats();
    println!("  Final Stats: {} batches, {} documents", 
             stats.total_batches_processed, stats.total_documents_processed);
    
    println!();
}
