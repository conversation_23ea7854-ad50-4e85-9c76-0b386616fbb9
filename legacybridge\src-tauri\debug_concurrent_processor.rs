// Debug test for ConcurrentProcessorV2 to isolate the hanging issue

use std::time::{Duration, Instant};
use legacybridge::pipeline::concurrent_processor_v2::ConcurrentProcessorV2;
use legacybridge::pipeline::concurrent_processor::{ConversionRequest, ConversionContent, ConversionOptions};

#[tokio::main]
async fn main() {
    println!("Debug ConcurrentProcessorV2 Test");
    println!("=================================\n");
    
    // Test 1: Create processor
    println!("1. Creating ConcurrentProcessorV2...");
    let processor = ConcurrentProcessorV2::new();
    println!("   ✅ Processor created successfully\n");
    
    // Test 2: Simple single request
    println!("2. Testing single conversion request...");
    let request = ConversionRequest {
        id: "test-1".to_string(),
        content: ConversionContent::Memory(r"{\rtf1\ansi\deff0 {\fonttbl {\f0 Times New Roman;}} \f0\fs24 Test document.}".to_string()),
        options: ConversionOptions::default(),
    };
    
    let start_time = Instant::now();
    println!("   Submitting request...");
    
    // Add timeout to prevent hanging
    match tokio::time::timeout(Duration::from_secs(10), processor.process_single(request)).await {
        Ok(response) => {
            let elapsed = start_time.elapsed();
            println!("   ✅ Request completed in {:?}", elapsed);
            println!("   Response ID: {}", response.id);
            match response.result {
                Ok(output) => println!("   Output length: {} bytes", output.len()),
                Err(e) => println!("   ❌ Conversion error: {:?}", e),
            }
        }
        Err(_) => {
            println!("   ❌ Request timed out after 10 seconds");
            return;
        }
    }
    
    println!("\n3. Testing batch processing...");
    let requests = vec![
        ConversionRequest {
            id: "batch-1".to_string(),
            content: ConversionContent::Memory(r"{\rtf1\ansi\deff0 {\fonttbl {\f0 Times New Roman;}} \f0\fs24 Batch document 1.}".to_string()),
            options: ConversionOptions::default(),
        },
        ConversionRequest {
            id: "batch-2".to_string(),
            content: ConversionContent::Memory(r"{\rtf1\ansi\deff0 {\fonttbl {\f0 Times New Roman;}} \f0\fs24 Batch document 2.}".to_string()),
            options: ConversionOptions::default(),
        },
    ];
    
    let start_time = Instant::now();
    println!("   Submitting batch of {} requests...", requests.len());
    
    match tokio::time::timeout(Duration::from_secs(15), processor.process_batch(requests)).await {
        Ok(responses) => {
            let elapsed = start_time.elapsed();
            println!("   ✅ Batch completed in {:?}", elapsed);
            println!("   Processed {} responses", responses.len());
            for response in responses {
                match response.result {
                    Ok(_) => println!("     ✅ {}: Success", response.id),
                    Err(e) => println!("     ❌ {}: Error: {:?}", response.id, e),
                }
            }
        }
        Err(_) => {
            println!("   ❌ Batch timed out after 15 seconds");
            return;
        }
    }
    
    println!("\n✅ ConcurrentProcessorV2 debug test completed successfully!");
}
