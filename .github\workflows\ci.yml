name: LegacyBridge CI/CD Pipeline

on:
  push:
    branches: [ main, develop, 'feature/*', 'hotfix/*' ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
        - staging
        - production
      skip_tests:
        description: 'Skip tests (emergency deployment only)'
        required: false
        default: false
        type: boolean

env:
  CARGO_TERM_COLOR: always
  RUST_BACKTRACE: 1
  NODE_VERSION: '20'
  RUST_VERSION: 'stable'

jobs:
  # Security scanning and dependency checks
  security-scan:
    name: Security Scanning
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: 'legacybridge/package-lock.json'

      - name: Setup Rust
        uses: actions-rs/toolchain@v1
        with:
          toolchain: ${{ env.RUST_VERSION }}
          override: true
          components: clippy

      - name: Install dependencies
        working-directory: legacybridge
        run: npm ci

      - name: Run npm audit
        working-directory: legacybridge
        run: |
          npm audit --audit-level=high --production
          echo "NPM audit completed"

      - name: Install cargo-audit
        run: cargo install cargo-audit

      - name: Run cargo audit
        working-directory: legacybridge/src-tauri
        run: |
          cargo audit
          echo "Cargo audit completed"

      - name: Run Clippy (Rust linting)
        working-directory: legacybridge/src-tauri
        run: |
          cargo clippy --all-targets --all-features -- -D warnings
          echo "Clippy analysis completed"

      - name: Initialize CodeQL
        uses: github/codeql-action/init@v3
        with:
          languages: javascript, rust

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3

  # Build matrix for multiple platforms
  build-matrix:
    name: Build (${{ matrix.os }})
    runs-on: ${{ matrix.os }}
    needs: security-scan
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        include:
          - os: ubuntu-latest
            target: x86_64-unknown-linux-gnu
            artifact_name: legacybridge-linux
          - os: windows-latest
            target: x86_64-pc-windows-msvc
            artifact_name: legacybridge-windows
          - os: macos-latest
            target: x86_64-apple-darwin
            artifact_name: legacybridge-macos

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: 'legacybridge/package-lock.json'

      - name: Setup Rust
        uses: actions-rs/toolchain@v1
        with:
          toolchain: ${{ env.RUST_VERSION }}
          override: true
          target: ${{ matrix.target }}

      - name: Cache Rust dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.cargo/registry
            ~/.cargo/git
            legacybridge/src-tauri/target
          key: ${{ runner.os }}-cargo-${{ hashFiles('**/Cargo.lock') }}
          restore-keys: |
            ${{ runner.os }}-cargo-

      - name: Install dependencies
        working-directory: legacybridge
        run: npm ci

      - name: Build frontend
        working-directory: legacybridge
        run: npm run build:frontend

      - name: Build Rust backend
        working-directory: legacybridge/src-tauri
        run: cargo build --release --target ${{ matrix.target }}

      - name: Run tests
        if: ${{ !inputs.skip_tests }}
        working-directory: legacybridge
        run: |
          npm run test:unit
          cd src-tauri && cargo test --release

      - name: Package artifacts
        shell: bash
        run: |
          mkdir -p artifacts
          if [[ "${{ matrix.os }}" == "windows-latest" ]]; then
            cp legacybridge/src-tauri/target/${{ matrix.target }}/release/legacybridge.exe artifacts/
            cp -r legacybridge/dist artifacts/frontend
          else
            cp legacybridge/src-tauri/target/${{ matrix.target }}/release/legacybridge artifacts/
            cp -r legacybridge/dist artifacts/frontend
          fi

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: ${{ matrix.artifact_name }}
          path: artifacts/
          retention-days: 30

  # Container building and pushing
  build-container:
    name: Build Container Images
    runs-on: ubuntu-latest
    needs: build-matrix
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop' || github.event_name == 'workflow_dispatch'
    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
      image-digest: ${{ steps.build.outputs.digest }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ghcr.io/${{ github.repository }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: legacybridge-linux
          path: artifacts/

      - name: Build and push Docker image
        id: build
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile.optimized
          platforms: linux/amd64,linux/arm64
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: ${{ steps.meta.outputs.tags }}
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

  # Integration and performance testing
  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    needs: build-container
    if: ${{ !inputs.skip_tests }}
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: testpass
          POSTGRES_DB: legacybridge_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: 'legacybridge/package-lock.json'

      - name: Install dependencies
        working-directory: legacybridge
        run: npm ci

      - name: Install Playwright browsers
        working-directory: legacybridge
        run: npx playwright install --with-deps

      - name: Run integration tests
        working-directory: legacybridge
        env:
          DATABASE_URL: postgresql://postgres:testpass@localhost:5432/legacybridge_test
        run: |
          npm run test:integration
          npm run test:e2e

      - name: Run performance tests
        working-directory: legacybridge
        run: |
          npm run test:performance:regression
          npm run test:performance:memory

      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: test-results
          path: |
            legacybridge/test-results/
            legacybridge/playwright-report/
          retention-days: 7

  # Deploy to staging
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [build-container, integration-tests]
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop'
    environment:
      name: staging
      url: https://staging.legacybridge.com
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'v1.28.0'

      - name: Configure kubectl
        run: |
          echo "${{ secrets.KUBE_CONFIG_STAGING }}" | base64 -d > kubeconfig
          export KUBECONFIG=kubeconfig

      - name: Deploy to staging
        run: |
          export KUBECONFIG=kubeconfig
          kubectl set image deployment/legacybridge-staging legacybridge=${{ needs.build-container.outputs.image-tag }}
          kubectl rollout status deployment/legacybridge-staging --timeout=300s

      - name: Run smoke tests
        run: |
          # Wait for deployment to be ready
          sleep 30
          # Run basic health checks
          curl -f https://staging.legacybridge.com/health || exit 1
          echo "Staging deployment successful"

  # Deploy to production (manual approval required)
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [build-container, deploy-staging]
    if: github.ref == 'refs/heads/main' || github.event_name == 'workflow_dispatch'
    environment:
      name: production
      url: https://legacybridge.com
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'v1.28.0'

      - name: Configure kubectl
        run: |
          echo "${{ secrets.KUBE_CONFIG_PRODUCTION }}" | base64 -d > kubeconfig
          export KUBECONFIG=kubeconfig

      - name: Blue-Green Deployment
        run: |
          export KUBECONFIG=kubeconfig

          # Create green deployment
          kubectl apply -f k8s/deployment.yaml
          kubectl set image deployment/legacybridge-green legacybridge=${{ needs.build-container.outputs.image-tag }}

          # Wait for green deployment to be ready
          kubectl rollout status deployment/legacybridge-green --timeout=600s

          # Run health checks on green
          kubectl port-forward deployment/legacybridge-green 8080:3030 &
          sleep 10
          curl -f http://localhost:8080/health || exit 1

          # Switch traffic to green
          kubectl patch service legacybridge -p '{"spec":{"selector":{"deployment":"green"}}}'

          # Monitor for 5 minutes
          sleep 300

          # Scale down blue deployment
          kubectl scale deployment legacybridge-blue --replicas=0

      - name: Post-deployment verification
        run: |
          # Run production smoke tests
          curl -f https://legacybridge.com/health || exit 1
          curl -f https://legacybridge.com/api/status || exit 1
          echo "Production deployment successful"

  # Notification and cleanup
  notify:
    name: Notify Results
    runs-on: ubuntu-latest
    needs: [security-scan, build-matrix, build-container, integration-tests, deploy-staging, deploy-production]
    if: always()
    steps:
      - name: Determine overall status
        id: status
        run: |
          if [[ "${{ needs.security-scan.result }}" == "failure" || "${{ needs.build-matrix.result }}" == "failure" || "${{ needs.build-container.result }}" == "failure" ]]; then
            echo "status=failure" >> $GITHUB_OUTPUT
          elif [[ "${{ needs.integration-tests.result }}" == "failure" ]]; then
            echo "status=test-failure" >> $GITHUB_OUTPUT
          elif [[ "${{ needs.deploy-production.result }}" == "success" ]]; then
            echo "status=deployed-production" >> $GITHUB_OUTPUT
          elif [[ "${{ needs.deploy-staging.result }}" == "success" ]]; then
            echo "status=deployed-staging" >> $GITHUB_OUTPUT
          else
            echo "status=build-success" >> $GITHUB_OUTPUT
          fi

      - name: Create deployment summary
        run: |
          echo "## 🚀 LegacyBridge CI/CD Pipeline Results" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Branch:** ${{ github.ref_name }}" >> $GITHUB_STEP_SUMMARY
          echo "**Commit:** ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY
          echo "**Status:** ${{ steps.status.outputs.status }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Job Results" >> $GITHUB_STEP_SUMMARY
          echo "- Security Scan: ${{ needs.security-scan.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- Build Matrix: ${{ needs.build-matrix.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- Container Build: ${{ needs.build-container.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- Integration Tests: ${{ needs.integration-tests.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- Deploy Staging: ${{ needs.deploy-staging.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- Deploy Production: ${{ needs.deploy-production.result }}" >> $GITHUB_STEP_SUMMARY
