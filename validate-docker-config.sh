#!/bin/bash
# Docker Configuration Validation Script
# Validates Docker setup without requiring <PERSON><PERSON> to be running

set -euo pipefail

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "info") echo -e "${BLUE}[INFO]${NC} $message" ;;
        "success") echo -e "${GREEN}[SUCCESS]${NC} $message" ;;
        "warning") echo -e "${YELLOW}[WARNING]${NC} $message" ;;
        "error") echo -e "${RED}[ERROR]${NC} $message" ;;
    esac
}

# Function to validate file exists
validate_file() {
    local file=$1
    local description=$2
    
    if [ -f "$file" ]; then
        print_status "success" "$description found: $file"
        return 0
    else
        print_status "error" "$description missing: $file"
        return 1
    fi
}

# Function to validate directory exists
validate_directory() {
    local dir=$1
    local description=$2
    
    if [ -d "$dir" ]; then
        print_status "success" "$description found: $dir"
        return 0
    else
        print_status "error" "$description missing: $dir"
        return 1
    fi
}

# Function to validate Dockerfile syntax
validate_dockerfile() {
    local dockerfile=$1
    
    print_status "info" "Validating Dockerfile syntax: $dockerfile"
    
    # Check for required instructions
    local required_instructions=("FROM" "WORKDIR" "COPY" "RUN" "EXPOSE" "CMD")
    local missing_instructions=()
    
    for instruction in "${required_instructions[@]}"; do
        if ! grep -q "^$instruction" "$dockerfile"; then
            missing_instructions+=("$instruction")
        fi
    done
    
    if [ ${#missing_instructions[@]} -eq 0 ]; then
        print_status "success" "All required Dockerfile instructions present"
    else
        print_status "warning" "Missing Dockerfile instructions: ${missing_instructions[*]}"
    fi
    
    # Check for security best practices
    if grep -q "USER.*root" "$dockerfile"; then
        print_status "warning" "Dockerfile runs as root user (security risk)"
    elif grep -q "USER" "$dockerfile"; then
        print_status "success" "Dockerfile uses non-root user"
    else
        print_status "warning" "No USER instruction found"
    fi
    
    # Check for health check
    if grep -q "HEALTHCHECK" "$dockerfile"; then
        print_status "success" "Health check configured"
    else
        print_status "warning" "No health check configured"
    fi
}

# Function to validate docker-compose.yml
validate_docker_compose() {
    local compose_file=$1
    
    print_status "info" "Validating docker-compose.yml: $compose_file"
    
    # Check for required sections
    local required_sections=("version" "services")
    local missing_sections=()
    
    for section in "${required_sections[@]}"; do
        if ! grep -q "^$section:" "$compose_file"; then
            missing_sections+=("$section")
        fi
    done
    
    if [ ${#missing_sections[@]} -eq 0 ]; then
        print_status "success" "All required docker-compose sections present"
    else
        print_status "error" "Missing docker-compose sections: ${missing_sections[*]}"
    fi
    
    # Check for security configurations
    if grep -q "restart:" "$compose_file"; then
        print_status "success" "Restart policy configured"
    else
        print_status "warning" "No restart policy configured"
    fi
    
    if grep -q "healthcheck:" "$compose_file"; then
        print_status "success" "Health checks configured in compose"
    else
        print_status "warning" "No health checks in compose file"
    fi
}

# Function to validate package.json scripts
validate_package_scripts() {
    local package_file="legacybridge/package.json"
    
    print_status "info" "Validating package.json scripts"
    
    # Check for required scripts
    local required_scripts=("build" "start")
    local missing_scripts=()
    
    for script in "${required_scripts[@]}"; do
        if ! grep -q "\"$script\":" "$package_file"; then
            missing_scripts+=("$script")
        fi
    done
    
    if [ ${#missing_scripts[@]} -eq 0 ]; then
        print_status "success" "All required npm scripts present"
    else
        print_status "warning" "Missing npm scripts: ${missing_scripts[*]}"
    fi
}

# Function to validate MCP server files
validate_mcp_server() {
    print_status "info" "Validating MCP server files"
    
    local mcp_files=(
        "legacybridge/src/mcp-server/index.ts"
        "legacybridge/src/mcp-server/core/mcp-server.ts"
        "legacybridge/scripts/start-mcp-server.sh"
    )
    
    local missing_files=()
    
    for file in "${mcp_files[@]}"; do
        if [ ! -f "$file" ]; then
            missing_files+=("$file")
        fi
    done
    
    if [ ${#missing_files[@]} -eq 0 ]; then
        print_status "success" "All MCP server files present"
    else
        print_status "warning" "Missing MCP server files: ${missing_files[*]}"
    fi
}

# Function to validate environment configuration
validate_environment() {
    print_status "info" "Validating environment configuration"
    
    # Check for .env.example or similar
    if [ -f ".env.example" ] || [ -f "legacybridge/.env.example" ]; then
        print_status "success" "Environment example file found"
    else
        print_status "warning" "No .env.example file found"
    fi
    
    # Check docker-compose for environment variables
    if [ -f "docker-compose.yml" ]; then
        if grep -q "environment:" "docker-compose.yml"; then
            print_status "success" "Environment variables configured in docker-compose"
        else
            print_status "warning" "No environment variables in docker-compose"
        fi
    fi
}

# Main validation function
main() {
    print_status "info" "Starting Docker configuration validation..."
    echo
    
    local validation_errors=0
    
    # Validate core files
    print_status "info" "=== Validating Core Files ==="
    validate_file "Dockerfile.optimized" "Optimized Dockerfile" || ((validation_errors++))
    validate_file "docker-compose.yml" "Docker Compose file" || ((validation_errors++))
    validate_file "legacybridge/package.json" "Package.json" || ((validation_errors++))
    echo
    
    # Validate directories
    print_status "info" "=== Validating Directories ==="
    validate_directory "legacybridge/src" "Source directory" || ((validation_errors++))
    validate_directory "legacybridge/src/mcp-server" "MCP server directory" || ((validation_errors++))
    validate_directory "legacybridge/scripts" "Scripts directory" || ((validation_errors++))
    echo
    
    # Validate Dockerfile
    print_status "info" "=== Validating Dockerfile ==="
    if [ -f "Dockerfile.optimized" ]; then
        validate_dockerfile "Dockerfile.optimized"
    fi
    echo
    
    # Validate docker-compose
    print_status "info" "=== Validating Docker Compose ==="
    if [ -f "docker-compose.yml" ]; then
        validate_docker_compose "docker-compose.yml"
    fi
    echo
    
    # Validate package.json
    print_status "info" "=== Validating Package Scripts ==="
    if [ -f "legacybridge/package.json" ]; then
        validate_package_scripts
    fi
    echo
    
    # Validate MCP server
    print_status "info" "=== Validating MCP Server ==="
    validate_mcp_server
    echo
    
    # Validate environment
    print_status "info" "=== Validating Environment ==="
    validate_environment
    echo
    
    # Summary
    print_status "info" "=== Validation Summary ==="
    if [ $validation_errors -eq 0 ]; then
        print_status "success" "Docker configuration validation completed successfully!"
        print_status "info" "Ready for Docker build and deployment"
    else
        print_status "warning" "Validation completed with $validation_errors errors/warnings"
        print_status "info" "Review the issues above before proceeding with Docker build"
    fi
    
    echo
    print_status "info" "Next steps:"
    echo "  1. Fix any validation errors"
    echo "  2. Build Docker image: docker build -f Dockerfile.optimized -t legacybridge:latest ."
    echo "  3. Test locally: docker run -p 3030:3030 legacybridge:latest"
    echo "  4. Deploy with docker-compose: docker-compose up -d"
    
    return $validation_errors
}

# Run main function
main "$@"
