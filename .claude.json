{"projects": {"/mnt/c/dev/legacy-bridge": {"allowedTools": [], "history": [], "mcpContextUris": [], "mcpServers": {"firebase": {"type": "stdio", "command": "/home/<USER>/.npm-global/bin/firebase", "args": ["experimental:mcp"], "env": {}, "timeout": 30000}, "fetch": {"type": "stdio", "command": "uvx", "args": ["mcp-server-fetch"], "env": {}, "timeout": 30000}, "sequential-thinking": {"type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "timeout": 30000}, "puppeteer": {"type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-puppeteer"], "timeout": 30000}, "mcp-playwright": {"type": "stdio", "command": "npx", "args": ["-y", "@executeautomation/playwright-mcp-server"], "timeout": 30000}, "everything": {"type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-everything"], "timeout": 30000}, "memory": {"type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "env": {"MEMORY_STORE_PATH": "/home/<USER>/memory-data/knowledge_graph.json"}, "timeout": 30000}, "memory-bank-mcp": {"type": "stdio", "command": "npx", "args": ["-y", "memory-bank-mcp"], "timeout": 30000}, "quick-data-mcp": {"type": "stdio", "command": "/home/<USER>/.local/bin/uv", "args": ["--directory", "/mnt/c/dev/legacy-bridge/quick-data-mcp/quick-data-mcp", "run", "python", "main.py"], "env": {"LOG_LEVEL": "INFO"}, "timeout": 30000}, "github-official": {"type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "*********************************************************************************************"}, "timeout": 30000}, "mcp-filesystem": {"type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/home/<USER>", "/mnt/c"], "timeout": 30000}, "desktop-commander": {"type": "stdio", "command": "npx", "args": ["-y", "@wonderwhy-er/desktop-commander"], "timeout": 30000}, "netlify": {"type": "stdio", "command": "npx", "args": ["-y", "@netlify/mcp"], "timeout": 30000}, "dart-mcp": {"type": "stdio", "command": "npx", "args": ["-y", "dart-mcp"], "timeout": 30000}, "dart": {"type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-dart"], "timeout": 30000}, "n8n-mcp": {"command": "npx", "args": ["n8n-mcp"], "env": {"MCP_MODE": "stdio", "LOG_LEVEL": "error", "DISABLE_CONSOLE_OUTPUT": "true", "N8N_API_URL": "https://kngpnn.app.n8n.cloud/api/v1", "N8N_API_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJmZjEzYjM0Yi1mNzE1LTRhNTYtOTRjMi0zYjViZmU2YTA2OWIiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzUzOTEyODA5LCJleHAiOjE3NjE2Mjc2MDB9.gGJG8PiBsQOYnITnhDjh7d3q8Trej0zUBUrXuy99-IE"}, "timeout": 30000}, "firecrawl": {"command": "npx", "args": ["-y", "firecrawl-mcp"], "env": {"FIRECRAWL_API_KEY": "fc-cb1a718eb720498dbe64d7a33c4de11f", "FIRECRAWL_RETRY_MAX_ATTEMPTS": "5", "FIRECRAWL_RETRY_INITIAL_DELAY": "2000", "FIRECRAWL_RETRY_MAX_DELAY": "30000", "FIRECRAWL_RETRY_BACKOFF_FACTOR": "3", "FIRECRAWL_CREDIT_WARNING_THRESHOLD": "2000", "FIRECRAWL_CREDIT_CRITICAL_THRESHOLD": "500"}}, "context7-mcp": {"type": "stdio", "command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"], "env": {"DEFAULT_MINIMUM_TOKENS": "6000"}, "timeout": 45000}, "consult7": {"type": "stdio", "command": "uvx", "args": ["consult7", "openrouter", "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"], "timeout": 30000}, "brave-search": {"type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "BSAV21R_O5a-txNQYigRkwYo5NnKlGn"}, "timeout": 30000}, "taskmanager": {"type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-taskmanager"], "timeout": 30000}, "taskmaster-ai": {"type": "stdio", "command": "npx", "args": ["-y", "--package=task-master-ai", "task-master-ai"], "env": {"ANTHROPIC_API_KEY": "************************************************************************************************************", "OPENROUTER_API_KEY": "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", "GOOGLE_API_KEY": "AIzaSyBoOp9NnzPL-eSkHYi_gZ9JLm4QehdF7SY", "PERPLEXITY_API_KEY": "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", "MCP_TIMEOUT": "300000", "MCP_TOOL_TIMEOUT": "180000"}, "timeout": 45000}, "agentic-tools-claude": {"type": "stdio", "command": "npx", "args": ["-y", "@pimzino/agentic-tools-mcp", "--claude"], "timeout": 30000}, "perplexity-mcp": {"type": "stdio", "command": "uvx", "args": ["perplexity-mcp"], "env": {"PERPLEXITY_API_KEY": "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", "PERPLEXITY_MODEL": "sonar-deep-research"}, "timeout": 30000}, "deep-code-reasoning": {"type": "stdio", "command": "node", "args": ["/home/<USER>/deep-code-reasoning-mcp/dist/index.js"], "env": {"GEMINI_API_KEY": "AIzaSyBoOp9NnzPL-eSkHYi_gZ9JLm4QehdF7SY"}, "timeout": 30000}, "shadcn-ui": {"type": "stdio", "command": "npx", "args": ["@jpisnice/shadcn-ui-mcp-server"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "****************************************"}, "timeout": 30000}, "nextjs-manager": {"type": "stdio", "command": "nextjs-mcp-server", "args": [], "timeout": 30000}, "vibe-coder-mcp-wsl": {"type": "stdio", "command": "wsl", "args": ["-d", "Ubuntu", "node", "/mnt/c/vibe-coder-mcp/build/index.js"], "cwd": "/mnt/c/vibe-coder-mcp", "env": {"LLM_CONFIG_PATH": "/mnt/c/vibe-coder-mcp/llm_config.json", "LOG_LEVEL": "debug", "NODE_ENV": "production", "VIBE_CODER_OUTPUT_DIR": "/mnt/c/vibe-coder-mcp/VibeCoderOutput", "CODE_MAP_ALLOWED_DIR": "/mnt/c/dev", "VIBE_TASK_MANAGER_READ_DIR": "/mnt/c/dev", "OPENROUTER_API_KEY": "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", "MOONSHOT_API_KEY": "sk-zetti8sDtQfDKmFXlQAjmQO05bp1bzlDHVU7nnGwzjNVCEzU", "PERPLEXITY_API_KEY": "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", "MOONSHOT_BASE_URL": "https://api.moonshot.ai/v1", "PERPLEXITY_BASE_URL": "https://api.perplexity.ai", "KIMI_MODEL": "kimi-k2-0711-preview", "PERPLEXITY_MODEL": "perplexity/sonar"}, "timeout": 30000}, "vibe-coder-mcp": {"type": "stdio", "command": "node", "args": ["/mnt/c/vibe-coder-mcp/build/index.js"], "cwd": "/mnt/c/vibe-coder-mcp", "env": {"LLM_CONFIG_PATH": "/mnt/c/vibe-coder-mcp/llm_config.json", "LOG_LEVEL": "debug", "NODE_ENV": "production", "VIBE_CODER_OUTPUT_DIR": "/mnt/c/vibe-coder-mcp/VibeCoderOutput", "CODE_MAP_ALLOWED_DIR": "/mnt/c/dev", "VIBE_TASK_MANAGER_READ_DIR": "/mnt/c/dev", "OPENROUTER_API_KEY": "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", "MOONSHOT_API_KEY": "sk-zetti8sDtQfDKmFXlQAjmQO05bp1bzlDHVU7nnGwzjNVCEzU", "PERPLEXITY_API_KEY": "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", "MOONSHOT_BASE_URL": "https://api.moonshot.ai/v1", "PERPLEXITY_BASE_URL": "https://api.perplexity.ai", "KIMI_MODEL": "kimi-k2-0711-preview", "PERPLEXITY_MODEL": "perplexity/sonar"}, "disabled": false, "autoApprove": ["research", "generate-rules", "generate-user-stories", "generate-task-list", "generate-prd", "generate-fullstack-starter-kit", "refactor-code", "git-summary", "run-workflow", "map-codebase"], "timeout": 30000}, "mcp-installer": {"type": "stdio", "command": "npx", "args": ["-y", "@anaisbetts/mcp-installer"], "timeout": 30000}}, "enabledMcpjsonServers": [], "disabledMcpjsonServers": [], "hasTrustDialogAccepted": false}}}