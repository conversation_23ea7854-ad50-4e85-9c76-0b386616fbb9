name: Multi-Architecture Build

on:
  workflow_call:
    inputs:
      release_tag:
        description: 'Release tag for the build'
        required: false
        type: string
      build_type:
        description: 'Type of build (release, debug)'
        required: false
        default: 'release'
        type: string
    outputs:
      artifacts:
        description: 'List of built artifacts'
        value: ${{ jobs.collect-artifacts.outputs.artifacts }}

  workflow_dispatch:
    inputs:
      release_tag:
        description: 'Release tag for the build'
        required: false
        type: string
      build_type:
        description: 'Type of build'
        required: false
        default: 'release'
        type: choice
        options:
        - release
        - debug

env:
  CARGO_TERM_COLOR: always
  RUST_BACKTRACE: 1

jobs:
  # Build native binaries for all platforms
  build-native:
    name: Build Native (${{ matrix.target }})
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        include:
          # Linux targets
          - os: ubuntu-latest
            target: x86_64-unknown-linux-gnu
            artifact_name: legacybridge-linux-x64
            cross: false
          - os: ubuntu-latest
            target: aarch64-unknown-linux-gnu
            artifact_name: legacybridge-linux-arm64
            cross: true
          - os: ubuntu-latest
            target: i686-unknown-linux-gnu
            artifact_name: legacybridge-linux-x86
            cross: true
          
          # Windows targets
          - os: windows-latest
            target: x86_64-pc-windows-msvc
            artifact_name: legacybridge-windows-x64
            cross: false
          - os: windows-latest
            target: i686-pc-windows-msvc
            artifact_name: legacybridge-windows-x86
            cross: false
          - os: windows-latest
            target: aarch64-pc-windows-msvc
            artifact_name: legacybridge-windows-arm64
            cross: true
          
          # macOS targets
          - os: macos-latest
            target: x86_64-apple-darwin
            artifact_name: legacybridge-macos-x64
            cross: false
          - os: macos-latest
            target: aarch64-apple-darwin
            artifact_name: legacybridge-macos-arm64
            cross: false

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Rust
        uses: actions-rs/toolchain@v1
        with:
          toolchain: stable
          override: true
          target: ${{ matrix.target }}

      - name: Install cross-compilation tools
        if: matrix.cross
        run: |
          cargo install cross --git https://github.com/cross-rs/cross

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
          cache-dependency-path: 'legacybridge/package-lock.json'

      - name: Cache Rust dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.cargo/registry
            ~/.cargo/git
            legacybridge/src-tauri/target
          key: ${{ runner.os }}-${{ matrix.target }}-cargo-${{ hashFiles('**/Cargo.lock') }}
          restore-keys: |
            ${{ runner.os }}-${{ matrix.target }}-cargo-
            ${{ runner.os }}-cargo-

      - name: Install frontend dependencies
        working-directory: legacybridge
        run: npm ci

      - name: Build frontend
        working-directory: legacybridge
        run: npm run build:frontend

      - name: Build Rust binary (native)
        if: '!matrix.cross'
        working-directory: legacybridge/src-tauri
        run: |
          if [[ "${{ inputs.build_type }}" == "release" ]]; then
            cargo build --release --target ${{ matrix.target }}
          else
            cargo build --target ${{ matrix.target }}
          fi

      - name: Build Rust binary (cross-compile)
        if: matrix.cross
        working-directory: legacybridge/src-tauri
        run: |
          if [[ "${{ inputs.build_type }}" == "release" ]]; then
            cross build --release --target ${{ matrix.target }}
          else
            cross build --target ${{ matrix.target }}
          fi

      - name: Package binary
        shell: bash
        run: |
          mkdir -p dist/${{ matrix.artifact_name }}
          
          # Determine build directory
          if [[ "${{ inputs.build_type }}" == "release" ]]; then
            BUILD_DIR="release"
          else
            BUILD_DIR="debug"
          fi
          
          # Copy binary
          if [[ "${{ matrix.os }}" == "windows-latest" ]]; then
            cp legacybridge/src-tauri/target/${{ matrix.target }}/${BUILD_DIR}/legacybridge.exe dist/${{ matrix.artifact_name }}/
          else
            cp legacybridge/src-tauri/target/${{ matrix.target }}/${BUILD_DIR}/legacybridge dist/${{ matrix.artifact_name }}/
          fi
          
          # Copy frontend assets
          cp -r legacybridge/dist dist/${{ matrix.artifact_name }}/frontend
          
          # Create version info
          echo "Version: ${{ inputs.release_tag || github.sha }}" > dist/${{ matrix.artifact_name }}/VERSION
          echo "Target: ${{ matrix.target }}" >> dist/${{ matrix.artifact_name }}/VERSION
          echo "Build Type: ${{ inputs.build_type }}" >> dist/${{ matrix.artifact_name }}/VERSION
          echo "Build Date: $(date -u)" >> dist/${{ matrix.artifact_name }}/VERSION

      - name: Create archive
        shell: bash
        run: |
          cd dist
          if [[ "${{ matrix.os }}" == "windows-latest" ]]; then
            7z a ${{ matrix.artifact_name }}.zip ${{ matrix.artifact_name }}/
          else
            tar -czf ${{ matrix.artifact_name }}.tar.gz ${{ matrix.artifact_name }}/
          fi

      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: ${{ matrix.artifact_name }}
          path: |
            dist/${{ matrix.artifact_name }}.zip
            dist/${{ matrix.artifact_name }}.tar.gz
          retention-days: 30

  # Build multi-architecture Docker images
  build-docker-multiarch:
    name: Build Multi-Arch Docker Images
    runs-on: ubuntu-latest
    needs: build-native
    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
      image-digest: ${{ steps.build.outputs.digest }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ghcr.io/${{ github.repository }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=${{ inputs.release_tag }},enable=${{ inputs.release_tag != '' }}
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Download Linux x64 artifact
        uses: actions/download-artifact@v4
        with:
          name: legacybridge-linux-x64
          path: artifacts/x64/

      - name: Download Linux ARM64 artifact
        uses: actions/download-artifact@v4
        with:
          name: legacybridge-linux-arm64
          path: artifacts/arm64/

      - name: Build and push multi-arch Docker image
        id: build
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile.optimized
          platforms: linux/amd64,linux/arm64
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          build-args: |
            BUILD_TYPE=${{ inputs.build_type }}
            VERSION=${{ inputs.release_tag || github.sha }}

  # Collect and summarize all artifacts
  collect-artifacts:
    name: Collect Artifacts
    runs-on: ubuntu-latest
    needs: [build-native, build-docker-multiarch]
    outputs:
      artifacts: ${{ steps.summary.outputs.artifacts }}
    steps:
      - name: Create artifact summary
        id: summary
        run: |
          ARTIFACTS=$(cat << 'EOF'
          {
            "native_binaries": [
              "legacybridge-linux-x64",
              "legacybridge-linux-arm64", 
              "legacybridge-linux-x86",
              "legacybridge-windows-x64",
              "legacybridge-windows-x86",
              "legacybridge-windows-arm64",
              "legacybridge-macos-x64",
              "legacybridge-macos-arm64"
            ],
            "docker_images": [
              "${{ needs.build-docker-multiarch.outputs.image-tag }}"
            ],
            "build_info": {
              "version": "${{ inputs.release_tag || github.sha }}",
              "build_type": "${{ inputs.build_type }}",
              "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)"
            }
          }
          EOF
          )
          echo "artifacts=${ARTIFACTS}" >> $GITHUB_OUTPUT

      - name: Create build summary
        run: |
          echo "## 🏗️ Multi-Architecture Build Complete" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Version:** ${{ inputs.release_tag || github.sha }}" >> $GITHUB_STEP_SUMMARY
          echo "**Build Type:** ${{ inputs.build_type }}" >> $GITHUB_STEP_SUMMARY
          echo "**Docker Image:** ${{ needs.build-docker-multiarch.outputs.image-tag }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Native Binaries Built" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Linux x64" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Linux ARM64" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Linux x86" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Windows x64" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Windows x86" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Windows ARM64" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ macOS x64" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ macOS ARM64" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Docker Images" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Multi-arch (linux/amd64, linux/arm64)" >> $GITHUB_STEP_SUMMARY
