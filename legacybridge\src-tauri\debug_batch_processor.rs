// Debug test for EnhancedBatchProcessor to isolate the hanging issue

use std::time::{Duration, Instant};
use uuid::Uuid;
use legacybridge::pipeline::enhanced_batch_processor::{
    EnhancedBatchProcessor, BatchProcessorConfig, BatchRequest, BatchPriority,
    BatchStatus
};
use legacybridge::pipeline::concurrent_processor::{ConversionRequest, ConversionContent, ConversionOptions};

#[tokio::main]
async fn main() {
    println!("Debug EnhancedBatchProcessor Test");
    println!("=================================\n");
    
    // Test 1: Create processor
    println!("1. Creating EnhancedBatchProcessor...");
    let processor = EnhancedBatchProcessor::new(BatchProcessorConfig::default());
    println!("   ✅ Processor created successfully\n");
    
    // Test 2: Submit batch but don't wait for completion
    println!("2. Testing batch submission (no waiting)...");
    let documents = vec![
        ConversionRequest {
            id: "debug-1".to_string(),
            content: ConversionContent::Memory(r"{\rtf1\ansi\deff0 {\fonttbl {\f0 Times New Roman;}} \f0\fs24 Debug document.}".to_string()),
            options: ConversionOptions::default(),
        }
    ];
    
    let batch_request = BatchRequest {
        batch_id: Uuid::new_v4().to_string(),
        documents,
        config: BatchProcessorConfig::default(),
        priority: BatchPriority::Normal,
        callback_url: None,
    };
    
    let start_time = Instant::now();
    println!("   Submitting batch: {}", batch_request.batch_id);
    
    match processor.submit_batch(batch_request.clone()).await {
        Ok(mut progress_rx) => {
            println!("   ✅ Batch submitted successfully");
            
            // Test 3: Try to receive one progress update with timeout
            println!("3. Waiting for first progress update...");
            match tokio::time::timeout(Duration::from_secs(5), progress_rx.recv()).await {
                Ok(Some(progress)) => {
                    println!("   ✅ Received progress update: {:?}", progress.status);
                    println!("   Progress: {:.1}%", progress.progress_percentage);
                }
                Ok(None) => {
                    println!("   ❌ Progress channel closed unexpectedly");
                }
                Err(_) => {
                    println!("   ❌ Timeout waiting for progress update");
                }
            }
            
            // Test 4: Check if we can get batch progress directly
            println!("4. Checking batch progress directly...");
            if let Some(progress) = processor.get_batch_progress(&batch_request.batch_id).await {
                println!("   ✅ Direct progress check: {:?}", progress.status);
                println!("   Documents: {}/{}", progress.completed_documents + progress.failed_documents, progress.total_documents);
            } else {
                println!("   ❌ Could not get batch progress");
            }
            
            // Test 5: Wait a bit more for completion
            println!("5. Waiting for completion (max 10 seconds)...");
            let mut completed = false;
            let timeout = Duration::from_secs(10);
            let wait_start = Instant::now();
            
            while wait_start.elapsed() < timeout && !completed {
                tokio::select! {
                    progress = progress_rx.recv() => {
                        if let Some(progress) = progress {
                            println!("   Progress: {:.1}% - Status: {:?}", 
                                     progress.progress_percentage, progress.status);
                            
                            if progress.status == BatchStatus::Completed || progress.status == BatchStatus::Failed {
                                let elapsed = start_time.elapsed();
                                println!("   ✅ Batch completed in {:?}", elapsed);
                                completed = true;
                            }
                        } else {
                            println!("   ❌ Progress channel closed");
                            break;
                        }
                    }
                    _ = tokio::time::sleep(Duration::from_millis(500)) => {
                        // Check progress directly every 500ms
                        if let Some(progress) = processor.get_batch_progress(&batch_request.batch_id).await {
                            println!("   Direct check: {:?} - {:.1}%", progress.status, progress.progress_percentage);
                            if progress.status == BatchStatus::Completed || progress.status == BatchStatus::Failed {
                                completed = true;
                            }
                        }
                    }
                }
            }
            
            if !completed {
                println!("   ⚠️ Batch did not complete within timeout");
            }
        }
        Err(e) => {
            println!("   ❌ Failed to submit batch: {:?}", e);
        }
    }
    
    println!("\n✅ Debug test completed!");
}
