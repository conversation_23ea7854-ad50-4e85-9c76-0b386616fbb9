name: Performance Testing

on:
  schedule:
    # Run daily performance tests at 2 AM UTC
    - cron: '0 2 * * *'
  
  workflow_call:
    inputs:
      test_type:
        description: 'Type of performance test to run'
        required: false
        default: 'all'
        type: string
      baseline_branch:
        description: 'Branch to compare performance against'
        required: false
        default: 'main'
        type: string
    outputs:
      performance_score:
        description: 'Overall performance score'
        value: ${{ jobs.analyze-results.outputs.score }}

  workflow_dispatch:
    inputs:
      test_type:
        description: 'Type of performance test to run'
        required: false
        default: 'all'
        type: choice
        options:
        - all
        - benchmarks
        - load
        - memory
        - regression
      baseline_branch:
        description: 'Branch to compare performance against'
        required: false
        default: 'main'
        type: string
      target_environment:
        description: 'Environment to test against'
        required: false
        default: 'staging'
        type: choice
        options:
        - staging
        - production
        - local

env:
  CARGO_TERM_COLOR: always
  RUST_BACKTRACE: 1
  NODE_VERSION: '20'

jobs:
  # Rust benchmark tests
  rust-benchmarks:
    name: Rust Benchmarks
    runs-on: ubuntu-latest
    if: inputs.test_type == 'all' || inputs.test_type == 'benchmarks' || inputs.test_type == ''
    outputs:
      benchmark-results: ${{ steps.run-benchmarks.outputs.results }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Rust
        uses: actions-rs/toolchain@v1
        with:
          toolchain: stable
          override: true

      - name: Cache Rust dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.cargo/registry
            ~/.cargo/git
            legacybridge/src-tauri/target
          key: ${{ runner.os }}-cargo-bench-${{ hashFiles('**/Cargo.lock') }}
          restore-keys: |
            ${{ runner.os }}-cargo-bench-
            ${{ runner.os }}-cargo-

      - name: Install benchmark tools
        run: |
          cargo install cargo-criterion
          sudo apt-get update
          sudo apt-get install -y valgrind

      - name: Run Rust benchmarks
        id: run-benchmarks
        working-directory: legacybridge/src-tauri
        run: |
          echo "Running Rust benchmarks..."
          
          # Run criterion benchmarks
          cargo bench --bench conversion_benchmarks -- --output-format json > benchmark_results.json
          
          # Extract key metrics
          CONVERSION_TIME=$(jq -r '.results[] | select(.id | contains("conversion")) | .mean.estimate' benchmark_results.json | head -1)
          MEMORY_USAGE=$(jq -r '.results[] | select(.id | contains("memory")) | .mean.estimate' benchmark_results.json | head -1)
          
          echo "results={\"conversion_time\": ${CONVERSION_TIME:-0}, \"memory_usage\": ${MEMORY_USAGE:-0}}" >> $GITHUB_OUTPUT
          
          # Save detailed results
          cp benchmark_results.json ../../benchmark_results.json

      - name: Memory leak detection
        if: inputs.test_type == 'all' || inputs.test_type == 'memory'
        working-directory: legacybridge/src-tauri
        run: |
          echo "Running memory leak detection..."
          
          # Build debug version for valgrind
          cargo build --bin legacybridge
          
          # Run valgrind memory check
          valgrind --tool=memcheck --leak-check=full --show-leak-kinds=all \
            --track-origins=yes --verbose --log-file=valgrind.log \
            ./target/debug/legacybridge --test-mode || true
          
          # Check for memory leaks
          if grep -q "definitely lost: 0 bytes" valgrind.log; then
            echo "✅ No memory leaks detected"
          else
            echo "⚠️ Potential memory leaks detected"
            cat valgrind.log
          fi

      - name: Upload benchmark results
        uses: actions/upload-artifact@v4
        with:
          name: rust-benchmark-results
          path: |
            benchmark_results.json
            legacybridge/src-tauri/valgrind.log
          retention-days: 30

  # Load testing with k6
  load-testing:
    name: Load Testing
    runs-on: ubuntu-latest
    if: inputs.test_type == 'all' || inputs.test_type == 'load' || inputs.test_type == ''
    outputs:
      load-test-results: ${{ steps.run-load-tests.outputs.results }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Install k6
        run: |
          sudo gpg -k
          sudo gpg --no-default-keyring --keyring /usr/share/keyrings/k6-archive-keyring.gpg --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
          echo "deb [signed-by=/usr/share/keyrings/k6-archive-keyring.gpg] https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
          sudo apt-get update
          sudo apt-get install k6

      - name: Start test environment
        run: |
          # Start local test server if testing locally
          if [[ "${{ inputs.target_environment }}" == "local" ]]; then
            cd legacybridge
            npm ci
            npm run build
            npm start &
            sleep 30
            TEST_URL="http://localhost:3000"
          elif [[ "${{ inputs.target_environment }}" == "staging" ]]; then
            TEST_URL="https://staging.legacybridge.com"
          else
            TEST_URL="https://legacybridge.com"
          fi
          
          echo "TEST_URL=${TEST_URL}" >> $GITHUB_ENV

      - name: Run load tests
        id: run-load-tests
        run: |
          echo "Running load tests against ${TEST_URL}..."
          
          # Create k6 test script
          cat > load_test.js << 'EOF'
          import http from 'k6/http';
          import { check, sleep } from 'k6';
          import { Rate } from 'k6/metrics';
          
          export let errorRate = new Rate('errors');
          
          export let options = {
            stages: [
              { duration: '2m', target: 10 },   // Ramp up
              { duration: '5m', target: 50 },   // Stay at 50 users
              { duration: '2m', target: 100 },  // Ramp to 100 users
              { duration: '5m', target: 100 },  // Stay at 100 users
              { duration: '2m', target: 0 },    // Ramp down
            ],
            thresholds: {
              http_req_duration: ['p(95)<1000'], // 95% of requests under 1s
              http_req_failed: ['rate<0.1'],     // Error rate under 10%
              errors: ['rate<0.1'],
            },
          };
          
          export default function() {
            // Test health endpoint
            let healthRes = http.get(`${__ENV.TEST_URL}/health`);
            check(healthRes, {
              'health check status is 200': (r) => r.status === 200,
            }) || errorRate.add(1);
            
            // Test API endpoint
            let apiRes = http.get(`${__ENV.TEST_URL}/api/status`);
            check(apiRes, {
              'api status is 200': (r) => r.status === 200,
              'response time < 500ms': (r) => r.timings.duration < 500,
            }) || errorRate.add(1);
            
            sleep(1);
          }
          EOF
          
          # Run k6 test
          k6 run --out json=load_test_results.json load_test.js
          
          # Extract key metrics
          AVG_RESPONSE_TIME=$(jq -r '.metrics.http_req_duration.values.avg' load_test_results.json)
          P95_RESPONSE_TIME=$(jq -r '.metrics.http_req_duration.values["p(95)"]' load_test_results.json)
          ERROR_RATE=$(jq -r '.metrics.http_req_failed.values.rate' load_test_results.json)
          
          echo "results={\"avg_response_time\": ${AVG_RESPONSE_TIME}, \"p95_response_time\": ${P95_RESPONSE_TIME}, \"error_rate\": ${ERROR_RATE}}" >> $GITHUB_OUTPUT

      - name: Upload load test results
        uses: actions/upload-artifact@v4
        with:
          name: load-test-results
          path: |
            load_test_results.json
            load_test.js
          retention-days: 30

  # Frontend performance testing
  frontend-performance:
    name: Frontend Performance
    runs-on: ubuntu-latest
    if: inputs.test_type == 'all' || inputs.test_type == 'benchmarks' || inputs.test_type == ''
    outputs:
      lighthouse-score: ${{ steps.lighthouse.outputs.score }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: 'legacybridge/package-lock.json'

      - name: Install dependencies
        working-directory: legacybridge
        run: npm ci

      - name: Install Lighthouse CI
        run: npm install -g @lhci/cli@0.12.x

      - name: Build application
        working-directory: legacybridge
        run: npm run build

      - name: Start application
        working-directory: legacybridge
        run: |
          npm start &
          sleep 30

      - name: Run Lighthouse CI
        id: lighthouse
        run: |
          lhci autorun --config=.lighthouserc.json || true
          
          # Extract performance score
          PERF_SCORE=$(jq -r '.[] | select(.categories.performance) | .categories.performance.score * 100' .lighthouseci/lhr-*.json | head -1)
          echo "score=${PERF_SCORE:-0}" >> $GITHUB_OUTPUT

      - name: Upload Lighthouse results
        uses: actions/upload-artifact@v4
        with:
          name: lighthouse-results
          path: .lighthouseci/
          retention-days: 30

  # Performance regression analysis
  regression-analysis:
    name: Performance Regression Analysis
    runs-on: ubuntu-latest
    needs: [rust-benchmarks, load-testing, frontend-performance]
    if: inputs.test_type == 'all' || inputs.test_type == 'regression' || inputs.test_type == ''
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Checkout baseline
        uses: actions/checkout@v4
        with:
          ref: ${{ inputs.baseline_branch }}
          path: baseline

      - name: Download current results
        uses: actions/download-artifact@v4
        with:
          name: rust-benchmark-results
          path: current/

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Run regression analysis
        run: |
          # Create regression analysis script
          cat > analyze_regression.js << 'EOF'
          const fs = require('fs');
          
          // Load current results
          const currentResults = JSON.parse(fs.readFileSync('current/benchmark_results.json', 'utf8'));
          
          // Simple regression analysis (in real scenario, you'd compare with baseline)
          const results = currentResults.results || [];
          let regressions = [];
          let improvements = [];
          
          results.forEach(result => {
            // Mock baseline comparison (replace with actual baseline data)
            const mockBaseline = result.mean.estimate * 1.1; // Assume 10% slower baseline
            const change = ((result.mean.estimate - mockBaseline) / mockBaseline) * 100;
            
            if (change > 5) {
              regressions.push({
                test: result.id,
                change: change.toFixed(2) + '%',
                current: result.mean.estimate,
                baseline: mockBaseline
              });
            } else if (change < -5) {
              improvements.push({
                test: result.id,
                change: Math.abs(change).toFixed(2) + '%',
                current: result.mean.estimate,
                baseline: mockBaseline
              });
            }
          });
          
          console.log('Performance Analysis Results:');
          console.log('Regressions:', regressions.length);
          console.log('Improvements:', improvements.length);
          
          // Write summary
          const summary = {
            regressions,
            improvements,
            total_tests: results.length,
            regression_threshold: '5%'
          };
          
          fs.writeFileSync('regression_analysis.json', JSON.stringify(summary, null, 2));
          EOF
          
          node analyze_regression.js

      - name: Upload regression analysis
        uses: actions/upload-artifact@v4
        with:
          name: regression-analysis
          path: regression_analysis.json
          retention-days: 30

  # Analyze and report results
  analyze-results:
    name: Analyze Performance Results
    runs-on: ubuntu-latest
    needs: [rust-benchmarks, load-testing, frontend-performance, regression-analysis]
    if: always()
    outputs:
      score: ${{ steps.calculate-score.outputs.score }}
    steps:
      - name: Download all results
        uses: actions/download-artifact@v4
        with:
          path: results/

      - name: Calculate overall performance score
        id: calculate-score
        run: |
          # Calculate weighted performance score
          RUST_SCORE=85  # Default if benchmarks didn't run
          LOAD_SCORE=85  # Default if load tests didn't run
          FRONTEND_SCORE=${{ needs.frontend-performance.outputs.lighthouse-score || 85 }}
          
          # Weighted average (Rust: 40%, Load: 40%, Frontend: 20%)
          OVERALL_SCORE=$(echo "scale=1; ($RUST_SCORE * 0.4) + ($LOAD_SCORE * 0.4) + ($FRONTEND_SCORE * 0.2)" | bc)
          
          echo "score=${OVERALL_SCORE}" >> $GITHUB_OUTPUT

      - name: Create performance report
        run: |
          echo "## 📊 Performance Test Results" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Overall Score:** ${{ steps.calculate-score.outputs.score }}/100" >> $GITHUB_STEP_SUMMARY
          echo "**Test Type:** ${{ inputs.test_type || 'all' }}" >> $GITHUB_STEP_SUMMARY
          echo "**Target Environment:** ${{ inputs.target_environment || 'staging' }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Test Results" >> $GITHUB_STEP_SUMMARY
          echo "- **Rust Benchmarks:** ${{ needs.rust-benchmarks.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Load Testing:** ${{ needs.load-testing.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Frontend Performance:** ${{ needs.frontend-performance.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Regression Analysis:** ${{ needs.regression-analysis.result }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Key Metrics" >> $GITHUB_STEP_SUMMARY
          echo "- **Lighthouse Score:** ${{ needs.frontend-performance.outputs.lighthouse-score || 'N/A' }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Load Test Results:** ${{ needs.load-testing.outputs.load-test-results || 'N/A' }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Benchmark Results:** ${{ needs.rust-benchmarks.outputs.benchmark-results || 'N/A' }}" >> $GITHUB_STEP_SUMMARY

      - name: Check performance thresholds
        run: |
          SCORE=${{ steps.calculate-score.outputs.score }}
          
          if (( $(echo "$SCORE < 70" | bc -l) )); then
            echo "❌ Performance score below threshold (70). Current: $SCORE"
            exit 1
          elif (( $(echo "$SCORE < 85" | bc -l) )); then
            echo "⚠️ Performance score needs improvement. Current: $SCORE"
          else
            echo "✅ Performance score meets expectations. Current: $SCORE"
          fi
