// Test Enhanced Input Validation System
// Validates comprehensive security-focused input validation

use legacybridge::conversion::enhanced_input_validation::{
    EnhancedInputValidator, EnhancedSecurityLimits
};

#[tokio::main]
async fn main() {
    println!("Enhanced Input Validation Test");
    println!("=============================\n");
    
    test_basic_validation();
    test_rtf_validation();
    test_markdown_validation();
    test_security_validation();
    test_performance_validation();
    test_custom_limits();
    
    println!("\n✅ All enhanced input validation tests completed successfully!");
}

fn test_basic_validation() {
    println!("1. Testing Basic Validation");
    println!("---------------------------");
    
    let validator = EnhancedInputValidator::new();
    
    // Test empty content
    let result = validator.validate_rtf_content("");
    assert!(result.is_err());
    println!("  ✅ Empty content rejected");
    
    // Test null bytes
    let malicious_content = "{\rtf1 Hello\0World}";
    let result = validator.validate_rtf_content(malicious_content);
    assert!(result.is_err());
    println!("  ✅ Null bytes detected and rejected");
    
    // Test excessive control characters
    let control_heavy = format!("{{\\rtf1 {}}}", "\x01\x02\x03".repeat(100));
    let result = validator.validate_rtf_content(&control_heavy);
    assert!(result.is_err());
    println!("  ✅ Excessive control characters rejected");
    
    // Test valid content
    let valid_rtf = r"{\rtf1\ansi\deff0 {\fonttbl {\f0 Times New Roman;}} \f0\fs24 Hello World.}";
    let result = validator.validate_rtf_content(valid_rtf);
    assert!(result.is_ok());
    println!("  ✅ Valid RTF content accepted");
    
    println!("  ✅ Basic validation tests passed\n");
}

fn test_rtf_validation() {
    println!("2. Testing RTF-Specific Validation");
    println!("----------------------------------");
    
    let validator = EnhancedInputValidator::new();
    
    // Test invalid RTF start
    let invalid_start = "rtf1 Hello World}";
    let result = validator.validate_rtf_content(invalid_start);
    assert!(result.is_err());
    println!("  ✅ Invalid RTF start rejected");
    
    // Test invalid RTF end
    let invalid_end = r"{\rtf1 Hello World";
    let result = validator.validate_rtf_content(invalid_end);
    assert!(result.is_err());
    println!("  ✅ Invalid RTF end rejected");
    
    // Test unbalanced braces
    let unbalanced = r"{\rtf1 {Hello World}";
    let result = validator.validate_rtf_content(unbalanced);
    assert!(result.is_err());
    println!("  ✅ Unbalanced braces rejected");
    
    // Test excessive nesting
    let deep_nesting = format!("{{\\rtf1 {}}}", "{".repeat(200) + "Hello" + &"}".repeat(200));
    let result = validator.validate_rtf_content(&deep_nesting);
    assert!(result.is_err());
    println!("  ✅ Excessive nesting rejected");
    
    // Test dangerous control words
    let dangerous_rtf = r"{\rtf1 \object Hello World}";
    let result = validator.validate_rtf_content(dangerous_rtf);
    assert!(result.is_err());
    println!("  ✅ Dangerous control words rejected");
    
    // Test valid RTF with tables
    let valid_table = r"{\rtf1\ansi\deff0 {\fonttbl {\f0 Times New Roman;}} \trowd\cellx1000\cellx2000 \intbl Cell1\cell Cell2\cell\row}";
    let result = validator.validate_rtf_content(valid_table);
    assert!(result.is_ok());
    println!("  ✅ Valid RTF with table accepted");
    
    println!("  ✅ RTF validation tests passed\n");
}

fn test_markdown_validation() {
    println!("3. Testing Markdown-Specific Validation");
    println!("---------------------------------------");
    
    let validator = EnhancedInputValidator::new();
    
    // Test script injection
    let script_injection = "# Hello\n<script>alert('xss')</script>";
    let result = validator.validate_markdown_content(script_injection);
    assert!(result.is_err());
    println!("  ✅ Script injection rejected");
    
    // Test javascript: protocol
    let js_protocol = "# Hello\n[Click here](javascript:alert('xss'))";
    let result = validator.validate_markdown_content(js_protocol);
    assert!(result.is_err());
    println!("  ✅ JavaScript protocol rejected");
    
    // Test dangerous HTML tags
    let dangerous_html = "# Hello\n<iframe src='http://evil.com'></iframe>";
    let result = validator.validate_markdown_content(dangerous_html);
    assert!(result.is_err());
    println!("  ✅ Dangerous HTML tags rejected");
    
    // Test event handlers
    let event_handler = "# Hello\n<img src='x' onerror='alert(1)'>";
    let result = validator.validate_markdown_content(event_handler);
    assert!(result.is_err());
    println!("  ✅ Event handlers rejected");
    
    // Test excessive links
    let many_links = format!("# Links\n{}", (0..150).map(|i| format!("[Link {}](http://example.com/{})", i, i)).collect::<Vec<_>>().join("\n"));
    let result = validator.validate_markdown_content(&many_links);
    assert!(result.is_err());
    println!("  ✅ Excessive links rejected");
    
    // Test valid markdown
    let valid_markdown = "# Hello World\n\nThis is a **bold** text with a [safe link](https://example.com).\n\n- Item 1\n- Item 2\n- Item 3";
    let result = validator.validate_markdown_content(valid_markdown);
    assert!(result.is_ok());
    println!("  ✅ Valid markdown accepted");
    
    println!("  ✅ Markdown validation tests passed\n");
}

fn test_security_validation() {
    println!("4. Testing Security Validation");
    println!("------------------------------");
    
    let validator = EnhancedInputValidator::new();
    
    // Test RTF with embedded objects
    let embedded_object = r"{\rtf1 \objdata 48656C6C6F}";
    let result = validator.validate_rtf_content(embedded_object);
    assert!(result.is_err());
    println!("  ✅ Embedded objects rejected");
    
    // Test RTF with excessive binary data
    let binary_heavy = format!("{{\\rtf1 {}}}", r"\bin1000 ".repeat(10));
    let result = validator.validate_rtf_content(&binary_heavy);
    assert!(result.is_err());
    println!("  ✅ Excessive binary data rejected");
    
    // Test markdown with data URLs
    let large_data_url = format!("![Image](data:image/png;base64,{})", "A".repeat(200_000));
    let result = validator.validate_markdown_content(&large_data_url);
    assert!(result.is_err());
    println!("  ✅ Large data URLs rejected");
    
    // Test markdown with suspicious protocols
    let suspicious_protocol = "[File](file:///etc/passwd)";
    let result = validator.validate_markdown_content(suspicious_protocol);
    assert!(result.is_err());
    println!("  ✅ Suspicious protocols rejected");
    
    // Test markdown with meta refresh
    let meta_refresh = "<meta http-equiv='refresh' content='0;url=http://evil.com'>";
    let result = validator.validate_markdown_content(meta_refresh);
    assert!(result.is_err());
    println!("  ✅ Meta refresh rejected");
    
    println!("  ✅ Security validation tests passed\n");
}

fn test_performance_validation() {
    println!("5. Testing Performance Validation");
    println!("---------------------------------");
    
    let validator = EnhancedInputValidator::new();
    
    // Test excessive table complexity in RTF
    let complex_table = format!("{{\\rtf1 {}}}", r"\trowd\cellx1000".repeat(2000));
    let result = validator.validate_rtf_content(&complex_table);
    assert!(result.is_err());
    println!("  ✅ Complex tables rejected");
    
    // Test excessive list nesting in markdown
    let deep_list = (0..25).map(|i| format!("{}- Item {}", "  ".repeat(i), i)).collect::<Vec<_>>().join("\n");
    let result = validator.validate_markdown_content(&deep_list);
    assert!(result.is_err());
    println!("  ✅ Deep list nesting rejected");
    
    // Test long lines
    let long_line = format!("# {}", "A".repeat(15_000));
    let result = validator.validate_markdown_content(&long_line);
    assert!(result.is_err());
    println!("  ✅ Long lines rejected");
    
    // Test reasonable complexity
    let reasonable_table = r"{\rtf1\ansi\deff0 \trowd\cellx1000\cellx2000\cellx3000 \intbl A\cell B\cell C\cell\row}";
    let result = validator.validate_rtf_content(reasonable_table);
    assert!(result.is_ok());
    println!("  ✅ Reasonable complexity accepted");
    
    println!("  ✅ Performance validation tests passed\n");
}

fn test_custom_limits() {
    println!("6. Testing Custom Security Limits");
    println!("---------------------------------");
    
    // Create validator with strict limits
    let strict_limits = EnhancedSecurityLimits {
        max_file_size: 1024, // 1KB
        max_nesting_depth: 5,
        max_control_words: 10,
        max_table_rows: 2,
        max_table_cols: 2,
        max_line_length: 100,
        max_links: 2,
        max_image_size_ref: 1024,
    };
    
    let strict_validator = EnhancedInputValidator::with_limits(strict_limits);
    
    // Test size limit
    let large_content = format!("{{\\rtf1 {}}}", "A".repeat(2000));
    let result = strict_validator.validate_rtf_content(&large_content);
    assert!(result.is_err());
    println!("  ✅ Custom size limit enforced");
    
    // Test nesting limit
    let deep_nesting = format!("{{\\rtf1 {}}}", "{".repeat(10) + "Hello" + &"}".repeat(10));
    let result = strict_validator.validate_rtf_content(&deep_nesting);
    assert!(result.is_err());
    println!("  ✅ Custom nesting limit enforced");
    
    // Test link limit
    let many_links = "[Link1](http://a.com)\n[Link2](http://b.com)\n[Link3](http://c.com)";
    let result = strict_validator.validate_markdown_content(many_links);
    assert!(result.is_err());
    println!("  ✅ Custom link limit enforced");
    
    // Test content that passes strict limits
    let simple_rtf = r"{\rtf1 Hello}";
    let result = strict_validator.validate_rtf_content(simple_rtf);
    assert!(result.is_ok());
    println!("  ✅ Simple content passes strict limits");
    
    println!("  ✅ Custom limits tests passed\n");
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_validator_creation() {
        let validator = EnhancedInputValidator::new();
        // Should not panic
        assert!(true);
    }

    #[test]
    fn test_basic_rtf_validation() {
        let validator = EnhancedInputValidator::new();
        let valid_rtf = r"{\rtf1\ansi\deff0 Hello World}";
        assert!(validator.validate_rtf_content(valid_rtf).is_ok());
    }

    #[test]
    fn test_basic_markdown_validation() {
        let validator = EnhancedInputValidator::new();
        let valid_markdown = "# Hello World\n\nThis is a test.";
        assert!(validator.validate_markdown_content(valid_markdown).is_ok());
    }

    #[test]
    fn test_security_rejection() {
        let validator = EnhancedInputValidator::new();
        let malicious_markdown = "<script>alert('xss')</script>";
        assert!(validator.validate_markdown_content(malicious_markdown).is_err());
    }
}
