// Error Adapter Module
// Provides conversion between legacy error types and the new unified error system

use super::error::{ConversionError, SystemError, ValidationError, ProcessingError};
use super::unified_error_system::{UnifiedError, UnifiedResult};

/// Trait for converting legacy errors to unified errors
pub trait ToUnifiedError {
    fn to_unified(self) -> UnifiedError;
}

/// Convert ConversionError to UnifiedError
impl ToUnifiedError for ConversionError {
    fn to_unified(self) -> UnifiedError {
        match self {
            ConversionError::Io(io_err) => UnifiedError::io_error(
                &io_err.to_string(),
                Some("IO operation"),
                None,
            ),
            
            ConversionError::Parser(msg) => UnifiedError::conversion_failed(
                &format!("Parser error: {}", msg),
            ),
            
            ConversionError::Lexer(msg) => UnifiedError::conversion_failed(
                &format!("Lexer error: {}", msg),
            ),
            
            ConversionError::Generator(msg) => UnifiedError::conversion_failed(
                &format!("Generator error: {}", msg),
            ),
            
            ConversionError::MemoryLimit { current, limit } => UnifiedError::resource_limit(
                "Memory limit exceeded",
                "memory",
                Some(current),
                Some(limit),
            ),
            
            ConversionError::InvalidFormat(msg) => UnifiedError::invalid_input(
                &format!("Invalid format: {}", msg),
            ),
            
            ConversionError::UnsupportedFormat(msg) => UnifiedError::conversion_failed(
                &format!("Unsupported format: {}", msg),
            ),
            
            ConversionError::InvalidInput(msg) => UnifiedError::invalid_input(&msg),
            
            ConversionError::Validation(msg) => UnifiedError::invalid_input(
                &format!("Validation error: {}", msg),
            ),
            
            ConversionError::Utf8(err) => UnifiedError::invalid_input(
                &format!("UTF-8 error: {}", err),
            ),
            
            ConversionError::FromUtf8(err) => UnifiedError::invalid_input(
                &format!("UTF-8 conversion error: {}", err),
            ),
            
            ConversionError::ParseInt(err) => UnifiedError::invalid_input(
                &format!("Integer parsing error: {}", err),
            ),
            
            ConversionError::Timeout(seconds) => UnifiedError::timeout(
                "Operation timed out",
                seconds,
            ),
            
            ConversionError::ResourceExhausted(msg) => UnifiedError::resource_limit(
                &msg,
                "resource",
                None,
                None,
            ),
            
            ConversionError::InvalidControlWord(word) => UnifiedError::conversion_failed(
                &format!("Invalid control word: {}", word),
            ),
            
            ConversionError::UnexpectedEof => UnifiedError::conversion_failed(
                "Unexpected end of input",
            ),
            
            ConversionError::GroupNesting(msg) => UnifiedError::conversion_failed(
                &format!("Group nesting error: {}", msg),
            ),
            
            ConversionError::NotImplemented(feature) => UnifiedError::system_error(
                &format!("Feature not implemented: {}", feature),
                "conversion",
            ),
            
            ConversionError::Security(msg) => UnifiedError::security_violation(
                &msg,
                "general",
            ),
            
            ConversionError::InvalidHex(value) => UnifiedError::conversion_failed(
                &format!("Invalid hex value: {}", value),
            ),
            
            ConversionError::Configuration(msg) => UnifiedError::system_error(
                &format!("Configuration error: {}", msg),
                "configuration",
            ),
            
            ConversionError::Ffi(msg) => UnifiedError::system_error(
                &format!("FFI error: {}", msg),
                "ffi",
            ),
        }
    }
}

/// Convert SystemError to UnifiedError
impl ToUnifiedError for SystemError {
    fn to_unified(self) -> UnifiedError {
        match self {
            SystemError::InsufficientMemory { required, available } => UnifiedError::resource_limit(
                "Insufficient memory",
                "memory",
                Some(available),
                Some(required),
            ),
            
            SystemError::FileSystem(io_err) => UnifiedError::io_error(
                &io_err.to_string(),
                Some("file system"),
                None,
            ),
            
            SystemError::Configuration(msg) => UnifiedError::system_error(
                &msg,
                "configuration",
            ),
            
            SystemError::ThreadPool(msg) => UnifiedError::system_error(
                &msg,
                "thread pool",
            ),
        }
    }
}

/// Convert ValidationError to UnifiedError
impl ToUnifiedError for ValidationError {
    fn to_unified(self) -> UnifiedError {
        match self {
            ValidationError::InvalidInput(msg) => UnifiedError::invalid_input(&msg),
            
            ValidationError::SizeLimit { actual, max } => UnifiedError::resource_limit(
                "Size limit exceeded",
                "size",
                Some(actual),
                Some(max),
            ),
            
            ValidationError::FormatMismatch { expected, actual } => UnifiedError::invalid_input_field(
                "format",
                &expected,
                &actual,
            ),
            
            ValidationError::InvalidEncoding(msg) => UnifiedError::invalid_input(
                &format!("Invalid encoding: {}", msg),
            ),
            
            ValidationError::MalformedStructure(msg) => UnifiedError::invalid_input(
                &format!("Malformed structure: {}", msg),
            ),
        }
    }
}

/// Convert ProcessingError to UnifiedError
impl ToUnifiedError for ProcessingError {
    fn to_unified(self) -> UnifiedError {
        match self {
            ProcessingError::ParsingFailed(msg) => UnifiedError::conversion_failed(
                &format!("Parsing failed: {}", msg),
            ),
            
            ProcessingError::ConversionError(msg) => UnifiedError::conversion_failed(&msg),
            
            ProcessingError::TimeoutExceeded(seconds) => UnifiedError::timeout(
                "Processing timed out",
                seconds,
            ),
            
            ProcessingError::ResourceLimit(msg) => UnifiedError::resource_limit(
                &msg,
                "resource",
                None,
                None,
            ),
        }
    }
}

/// Extension trait for Result<T, E> to convert to UnifiedResult<T>
pub trait ToUnifiedResult<T, E: ToUnifiedError> {
    fn to_unified_result(self) -> UnifiedResult<T>;
}

impl<T, E: ToUnifiedError> ToUnifiedResult<T, E> for Result<T, E> {
    fn to_unified_result(self) -> UnifiedResult<T> {
        self.map_err(|e| e.to_unified())
    }
}

/// Convert from UnifiedError to legacy ConversionError
impl From<UnifiedError> for ConversionError {
    fn from(error: UnifiedError) -> Self {
        match error {
            UnifiedError::InvalidInput { message, .. } => ConversionError::InvalidInput(message),
            UnifiedError::ConversionFailed { message, .. } => ConversionError::Parser(message),
            UnifiedError::IoError { message, .. } => ConversionError::Io(std::io::Error::new(
                std::io::ErrorKind::Other,
                message,
            )),
            UnifiedError::ResourceLimit { message, .. } => ConversionError::ResourceExhausted(message),
            UnifiedError::SecurityViolation { message, .. } => ConversionError::Security(message),
            UnifiedError::Timeout { timeout_seconds, .. } => ConversionError::Timeout(timeout_seconds),
            UnifiedError::SystemError { message, .. } => ConversionError::Configuration(message),
            UnifiedError::InternalError { .. } => ConversionError::Configuration(
                "Internal error occurred".to_string(),
            ),
        }
    }
}
