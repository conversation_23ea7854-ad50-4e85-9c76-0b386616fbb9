# Multi-stage Dockerfile for LegacyBridge MCP Server - Production Optimized
# Supports multi-architecture builds (amd64, arm64) with enhanced security
# Builds and runs the MCP server for web deployment

# Build arguments for versioning and configuration
ARG VERSION=unknown
ARG BUILD_DATE=unknown
ARG COMMIT_SHA=unknown
ARG BUILD_TYPE=release
ARG TARGETPLATFORM
ARG BUILDPLATFORM

# Stage 1: Base dependencies layer (cached across builds)
FROM --platform=$BUILDPLATFORM node:20-alpine AS base-deps

# Install build dependencies with security hardening
RUN apk add --no-cache \
    libc6-compat \
    python3 \
    make \
    g++ \
    git \
    ca-certificates \
    && apk upgrade --no-cache \
    && rm -rf /var/cache/apk/* \
    && npm config set audit-level moderate

# Stage 2: Node.js dependency installer with security scanning
FROM --platform=$BUILDPLATFORM base-deps AS node-deps

WORKDIR /deps

# Copy package files only
COPY legacybridge/package*.json ./

# Install production dependencies with security checks
RUN npm ci --only=production --no-audit --no-fund \
    && npm audit --audit-level=high --production \
    && npm cache clean --force \
    # Remove unnecessary files to reduce image size
    && find node_modules -name "*.md" -delete \
    && find node_modules -name "*.txt" -delete \
    && find node_modules -name "test" -type d -exec rm -rf {} + 2>/dev/null || true \
    && find node_modules -name "tests" -type d -exec rm -rf {} + 2>/dev/null || true

# Stage 3: Rust builder for cross-compilation
FROM --platform=$BUILDPLATFORM rust:1.75-alpine AS rust-builder

# Install cross-compilation dependencies
RUN apk add --no-cache \
    musl-dev \
    gcc \
    libc-dev \
    && if [ "$TARGETPLATFORM" = "linux/arm64" ]; then \
        apk add --no-cache gcc-aarch64-none-elf; \
    fi

WORKDIR /rust-build

# Copy Rust source
COPY legacybridge/src-tauri ./

# Set target based on platform
RUN case "$TARGETPLATFORM" in \
    "linux/amd64") TARGET="x86_64-unknown-linux-musl" ;; \
    "linux/arm64") TARGET="aarch64-unknown-linux-musl" ;; \
    *) TARGET="x86_64-unknown-linux-musl" ;; \
    esac \
    && rustup target add $TARGET \
    && if [ "$BUILD_TYPE" = "release" ]; then \
        cargo build --release --target $TARGET; \
    else \
        cargo build --target $TARGET; \
    fi

# Stage 4: Node.js builder for MCP server
FROM --platform=$BUILDPLATFORM base-deps AS node-builder

WORKDIR /build

# Copy dependencies from previous stage
COPY --from=node-deps /deps/node_modules ./node_modules

# Copy source files
COPY legacybridge/package*.json ./
COPY legacybridge/src ./src
COPY legacybridge/tsconfig*.json ./
COPY legacybridge/scripts ./scripts
COPY legacybridge/next.config.ts ./

# Set build environment variables
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

# Build application with optimizations
RUN npm run build:frontend \
    && mkdir -p dist/mcp-server \
    && cp -r src/mcp-server/* dist/mcp-server/ 2>/dev/null || true \
    # Optimize built files
    && find dist -name "*.map" -delete \
    && find dist -name "*.test.*" -delete

# Stage 5: Security scanner with comprehensive checks
FROM --platform=$BUILDPLATFORM aquasec/trivy:latest AS security-scanner
WORKDIR /scan
COPY --from=node-builder /build/package*.json ./
COPY --from=node-builder /build/node_modules ./node_modules
RUN trivy fs --no-progress --security-checks vuln,config,secret . \
    && trivy fs --format json --output scan-results.json .

# Stage 6: Final production image with enhanced security
FROM --platform=$TARGETPLATFORM node:20-alpine AS production

# Install runtime dependencies with security hardening
RUN apk add --no-cache \
    libc6-compat \
    openssl \
    curl \
    dumb-init \
    && apk upgrade --no-cache \
    && rm -rf /var/cache/apk/* \
    # Create non-root user with restricted permissions
    && addgroup -g 1001 -S nodejs \
    && adduser -S mcpuser -u 1001 -G nodejs \
    # Remove unnecessary packages and files
    && rm -rf /usr/share/man/* \
    && rm -rf /tmp/* \
    && rm -rf /var/tmp/*

# Set working directory
WORKDIR /app

# Copy built artifacts with proper ownership and minimal permissions
COPY --from=node-builder --chown=mcpuser:nodejs /build/dist ./dist
COPY --from=node-builder --chown=mcpuser:nodejs /build/node_modules ./node_modules
COPY --from=node-builder --chown=mcpuser:nodejs /build/package*.json ./
COPY --from=node-builder --chown=mcpuser:nodejs /build/scripts ./scripts

# Create bin directory for Rust binary
RUN mkdir -p /app/bin && chown mcpuser:nodejs /app/bin

# Copy Rust binary if available (conditional copy using shell)
RUN if [ -f /rust-build/target/*/release/legacybridge ]; then \
        cp /rust-build/target/*/release/legacybridge /app/bin/legacybridge; \
    elif [ -f /rust-build/target/*/debug/legacybridge ]; then \
        cp /rust-build/target/*/debug/legacybridge /app/bin/legacybridge; \
    else \
        echo "No Rust binary found, using Node.js only"; \
    fi || true

# Copy header files
COPY --chown=mcpuser:nodejs legacybridge/include/legacybridge.h ./include/

# Create necessary directories with proper permissions
RUN mkdir -p /app/logs /app/uploads /app/output /app/temp /app/cache \
    && chown -R mcpuser:nodejs /app/logs /app/uploads /app/output /app/temp /app/cache \
    && chmod 755 /app/logs /app/uploads /app/output /app/temp /app/cache \
    # Set strict permissions on sensitive files
    && chmod 644 /app/package*.json \
    && chmod -R 755 /app/dist \
    && chmod -R 755 /app/scripts

# Set environment variables with security considerations
ENV NODE_ENV=production \
    MCP_PORT=3030 \
    LOG_LEVEL=info \
    CACHE_ENABLED=false \
    ENABLE_DOC=false \
    ENABLE_WORDPERFECT=false \
    NODE_OPTIONS="--max-old-space-size=512 --enable-source-maps=false" \
    NPM_CONFIG_UPDATE_NOTIFIER=false \
    NPM_CONFIG_FUND=false \
    NEXT_TELEMETRY_DISABLED=1

# Security hardening
RUN echo "mcpuser:!:$(date +%s):0:99999:7:::" > /etc/shadow

# Switch to non-root user
USER mcpuser

# Expose MCP server port
EXPOSE 3030

# Enhanced health check with multiple endpoints
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:3030/health && \
        curl -f http://localhost:3030/api/status || exit 1

# Add comprehensive metadata labels
LABEL org.opencontainers.image.title="LegacyBridge MCP Server" \
      org.opencontainers.image.description="Enterprise RTF-Markdown Converter MCP Server with Multi-Architecture Support" \
      org.opencontainers.image.version="${VERSION}" \
      org.opencontainers.image.created="${BUILD_DATE}" \
      org.opencontainers.image.revision="${COMMIT_SHA}" \
      org.opencontainers.image.licenses="MIT" \
      org.opencontainers.image.vendor="LegacyBridge Team" \
      org.opencontainers.image.source="https://github.com/legacybridge/legacybridge" \
      org.opencontainers.image.documentation="https://github.com/legacybridge/legacybridge/blob/main/README.md" \
      org.opencontainers.image.url="https://legacybridge.com" \
      maintainer="LegacyBridge Team <<EMAIL>>" \
      build.type="${BUILD_TYPE}" \
      build.platform="${TARGETPLATFORM}"

# Use dumb-init for proper signal handling and start the MCP server
ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "dist/mcp-server/index.js"]

# Stage 7: Debug image (optional, for troubleshooting)
FROM production AS debug

# Switch back to root for installing debug tools
USER root

# Install comprehensive debugging and monitoring tools
RUN apk add --no-cache \
    bash \
    vim \
    nano \
    curl \
    wget \
    netcat-openbsd \
    procps \
    htop \
    strace \
    tcpdump \
    iotop \
    lsof \
    tree \
    jq \
    && rm -rf /var/cache/apk/*

# Add debugging aliases and environment
RUN echo 'alias ll="ls -la"' >> /home/<USER>/.bashrc \
    && echo 'alias logs="tail -f /app/logs/*.log"' >> /home/<USER>/.bashrc \
    && echo 'alias ps-node="ps aux | grep node"' >> /home/<USER>/.bashrc

# Switch back to non-root user
USER mcpuser

# Override CMD for debug mode (interactive shell)
CMD ["bash"]

# Stage 8: Minimal production image (smallest possible)
FROM --platform=$TARGETPLATFORM node:20-alpine AS minimal

# Install only essential runtime dependencies
RUN apk add --no-cache \
    libc6-compat \
    dumb-init \
    && addgroup -g 1001 -S nodejs \
    && adduser -S mcpuser -u 1001 -G nodejs \
    && rm -rf /var/cache/apk/*

WORKDIR /app

# Copy only essential files
COPY --from=node-builder --chown=mcpuser:nodejs /build/dist ./dist
COPY --from=node-builder --chown=mcpuser:nodejs /build/package.json ./

# Install only production runtime dependencies
RUN npm ci --only=production --no-audit --no-fund \
    && npm cache clean --force \
    && rm -rf /tmp/* /var/tmp/*

# Create minimal directory structure
RUN mkdir -p logs temp && chown -R mcpuser:nodejs logs temp

# Set minimal environment
ENV NODE_ENV=production \
    MCP_PORT=3030 \
    NODE_OPTIONS="--max-old-space-size=256"

USER mcpuser
EXPOSE 3030

# Minimal health check
HEALTHCHECK --interval=60s --timeout=5s --start-period=30s --retries=2 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:3030/health || exit 1

ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "dist/mcp-server/index.js"]