// Comprehensive test for all legacy format support
// Tests DOC, WordPerfect, dBase, WordStar, and Lotus 1-2-3 formats

use crate::formats::{FormatManager, FormatType};
use crate::formats::{doc, wordperf, dbase, wordstar, lotus};
use crate::ffi_legacy::*;
use std::ffi::CString;

#[cfg(test)]
mod legacy_format_integration_tests {
    use super::*;

    #[test]
    fn test_legacy_format_manager_initialization() {
        // Test FFI initialization
        let result = LegacyBridge_Initialize();
        assert_eq!(result, 0, "Legacy bridge initialization should succeed");
        
        // Test cleanup
        let cleanup_result = LegacyBridge_Cleanup();
        assert_eq!(cleanup_result, 0, "Legacy bridge cleanup should succeed");
    }

    #[test]
    fn test_doc_format_detection_and_conversion() {
        // Initialize
        assert_eq!(LegacyBridge_Initialize(), 0);
        
        // Create a minimal DOC file signature
        let doc_content = create_mock_doc_content();
        
        // Test format detection
        let mut confidence = 0.0f32;
        let format_type = LegacyBridge_DetectFormat(
            doc_content.as_ptr(),
            doc_content.len() as i32,
            &mut confidence as *mut f32
        );
        
        // Should detect as DOC format (type 1)
        assert_eq!(format_type, 1, "Should detect DOC format");
        assert!(confidence > 0.0, "Should have some confidence in detection");
        
        // Cleanup
        LegacyBridge_Cleanup();
    }

    #[test]
    fn test_wordperfect_format_detection() {
        assert_eq!(LegacyBridge_Initialize(), 0);
        
        let wp_content = create_mock_wordperfect_content();
        let mut confidence = 0.0f32;
        let format_type = LegacyBridge_DetectFormat(
            wp_content.as_ptr(),
            wp_content.len() as i32,
            &mut confidence as *mut f32
        );
        
        // Should detect as WordPerfect format (type 2)
        assert_eq!(format_type, 2, "Should detect WordPerfect format");
        
        LegacyBridge_Cleanup();
    }

    #[test]
    fn test_dbase_format_detection() {
        assert_eq!(LegacyBridge_Initialize(), 0);
        
        let dbase_content = create_mock_dbase_content();
        let mut confidence = 0.0f32;
        let format_type = LegacyBridge_DetectFormat(
            dbase_content.as_ptr(),
            dbase_content.len() as i32,
            &mut confidence as *mut f32
        );
        
        // Should detect as dBase format (type 3)
        assert_eq!(format_type, 3, "Should detect dBase format");
        
        LegacyBridge_Cleanup();
    }

    #[test]
    fn test_wordstar_format_detection() {
        assert_eq!(LegacyBridge_Initialize(), 0);
        
        let wordstar_content = create_mock_wordstar_content();
        let mut confidence = 0.0f32;
        let format_type = LegacyBridge_DetectFormat(
            wordstar_content.as_ptr(),
            wordstar_content.len() as i32,
            &mut confidence as *mut f32
        );
        
        // Should detect as WordStar format (type 4)
        assert_eq!(format_type, 4, "Should detect WordStar format");
        
        LegacyBridge_Cleanup();
    }

    #[test]
    fn test_lotus_format_detection() {
        assert_eq!(LegacyBridge_Initialize(), 0);
        
        let lotus_content = create_mock_lotus_content();
        let mut confidence = 0.0f32;
        let format_type = LegacyBridge_DetectFormat(
            lotus_content.as_ptr(),
            lotus_content.len() as i32,
            &mut confidence as *mut f32
        );
        
        // Should detect as Lotus 1-2-3 format (type 5)
        assert_eq!(format_type, 5, "Should detect Lotus 1-2-3 format");
        
        LegacyBridge_Cleanup();
    }

    #[test]
    fn test_version_and_supported_formats() {
        // Test version retrieval
        let mut version_buffer = vec![0u8; 64];
        let result = LegacyBridge_GetVersion(
            version_buffer.as_mut_ptr() as *mut i8,
            version_buffer.len() as i32
        );
        assert_eq!(result, 0, "Should successfully get version");

        // Test supported formats
        let formats_result = LegacyBridge_GetSupportedFormats();
        assert!(formats_result > 0, "Should support multiple formats");
    }

    // Test that format manager can be created and has expected capabilities
    #[test]
    fn test_format_manager_capabilities() {
        let manager = FormatManager::new();

        // Test that all expected format types are supported
        let supported = manager.supported_formats();
        assert!(supported.contains(&FormatType::Doc), "Should support DOC format");
        assert!(supported.contains(&FormatType::WordPerfect), "Should support WordPerfect format");
        assert!(supported.contains(&FormatType::DBase), "Should support dBase format");
        assert!(supported.contains(&FormatType::WordStar), "Should support WordStar format");
        assert!(supported.contains(&FormatType::Lotus123), "Should support Lotus 1-2-3 format");
        assert_eq!(supported.len(), 5, "Should support exactly 5 formats");

        // Test unknown content returns Unknown format
        let empty_content = vec![0u8; 10];
        let detection = manager.detect_format(&empty_content).unwrap();
        assert_eq!(detection.format_type, FormatType::Unknown);
    }

    #[test]
    fn test_format_conversion_error_handling() {
        let manager = FormatManager::new();

        // Test conversion with empty content - should handle gracefully
        let empty_content = vec![];
        let result = manager.convert_to_markdown(&empty_content, &FormatType::Doc);
        assert!(result.is_err(), "Empty content should return error");

        // Test conversion with disabled format using with_features
        let mut disabled_features = std::collections::HashMap::new();
        disabled_features.insert(FormatType::Doc, false);
        disabled_features.insert(FormatType::WordPerfect, true);
        disabled_features.insert(FormatType::DBase, true);
        disabled_features.insert(FormatType::WordStar, true);
        disabled_features.insert(FormatType::Lotus123, true);

        let disabled_manager = FormatManager::with_features(disabled_features);
        let supported = disabled_manager.supported_formats();
        assert!(!supported.contains(&FormatType::Doc), "DOC should be disabled");
        assert_eq!(supported.len(), 4, "Should have 4 enabled formats");

        let content = vec![0u8; 100];
        let result = disabled_manager.convert_to_markdown(&content, &FormatType::Doc);
        assert!(result.is_err(), "Disabled format should return error");
    }

    // Helper functions to create mock file content with proper signatures

    fn create_mock_doc_content() -> Vec<u8> {
        let mut content = vec![0u8; 512];
        // OLE2 signature
        content[0..8].copy_from_slice(&[0xD0, 0xCF, 0x11, 0xE0, 0xA1, 0xB1, 0x1A, 0xE1]);
        // Minor version
        content[28] = 0xFE;
        content[29] = 0xFF;
        // Sector size (512 bytes = 2^9)
        content[30] = 9;
        content
    }

    fn create_mock_wordperfect_content() -> Vec<u8> {
        let mut content = vec![0u8; 32];
        // WordPerfect signature
        content[0..4].copy_from_slice(&[0xFF, 0x57, 0x50, 0x43]);
        // Version 6.0
        content[20] = 6;
        content[21] = 0;
        content
    }

    fn create_mock_dbase_content() -> Vec<u8> {
        let mut content = vec![0u8; 32];
        // dBase III signature
        content[0] = 0x03;
        // Header length
        content[8] = 32;
        content[9] = 0;
        // Record length
        content[10] = 10;
        content[11] = 0;
        content
    }

    fn create_mock_wordstar_content() -> Vec<u8> {
        // WordStar content with control characters and dot commands
        b"\x02Hello\x02 World\n.PL 66\nThis is a test document.".to_vec()
    }

    fn create_mock_lotus_content() -> Vec<u8> {
        let mut content = vec![0u8; 16];
        // Lotus WK1 signature
        content[0..4].copy_from_slice(&[0x00, 0x00, 0x02, 0x00]);
        content
    }

    // More realistic content for conversion testing
    fn create_realistic_doc_content() -> Vec<u8> {
        let mut content = vec![0u8; 1024];
        // OLE2 signature
        content[0..8].copy_from_slice(&[0xD0, 0xCF, 0x11, 0xE0, 0xA1, 0xB1, 0x1A, 0xE1]);
        content[28] = 0xFE;
        content[29] = 0xFF;
        content[30] = 9;
        // Add some mock document content
        let text = b"Hello World Document";
        content[512..512+text.len()].copy_from_slice(text);
        content
    }

    fn create_realistic_dbase_content() -> Vec<u8> {
        let mut content = vec![0u8; 128];
        // dBase III signature
        content[0] = 0x03;
        // Header length (32 bytes)
        content[8] = 32;
        content[9] = 0;
        // Record length (20 bytes)
        content[10] = 20;
        content[11] = 0;
        // Number of records (1)
        content[4] = 1;
        content[5] = 0;
        content[6] = 0;
        content[7] = 0;
        // Add field descriptor
        content[32..42].copy_from_slice(b"NAME      ");
        content[43] = b'C'; // Character field
        content[47] = 10; // Field length
        content
    }

    fn create_realistic_wordstar_content() -> Vec<u8> {
        b".PL 66\n.MT 6\n.MB 6\n\x02TITLE\x02\n\nThis is a WordStar document with \x02bold\x02 text and \x13underlined\x13 text.\n\n.PA\nPage 2 content here.".to_vec()
    }
}
