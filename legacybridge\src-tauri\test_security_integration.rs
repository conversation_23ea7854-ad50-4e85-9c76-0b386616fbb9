// Test Security Integration
// Validates that enhanced input validation is properly integrated across all interfaces

use std::ffi::{CString, CStr};
use std::os::raw::{c_char, c_int};
use std::ptr;

use legacybridge::conversion::{rtf_to_markdown, markdown_to_rtf, secure_rtf_to_markdown, secure_markdown_to_rtf};
use legacybridge::ffi_unified::*;

#[tokio::main]
async fn main() {
    println!("Security Integration Test");
    println!("========================\n");
    
    test_conversion_functions_security();
    test_ffi_security_integration();
    test_malicious_content_rejection();
    test_performance_limits();
    
    println!("\n✅ All security integration tests completed successfully!");
}

fn test_conversion_functions_security() {
    println!("1. Testing Conversion Functions Security");
    println!("---------------------------------------");
    
    // Test malicious RTF rejection
    let malicious_rtf = r"{\rtf1 \object Hello World}";
    let result = rtf_to_markdown(malicious_rtf);
    assert!(result.is_err());
    println!("  ✅ Malicious RTF rejected by rtf_to_markdown");
    
    let result = secure_rtf_to_markdown(malicious_rtf);
    assert!(result.is_err());
    println!("  ✅ Malicious RTF rejected by secure_rtf_to_markdown");
    
    // Test malicious Markdown rejection
    let malicious_markdown = "# Hello\n<script>alert('xss')</script>";
    let result = markdown_to_rtf(malicious_markdown);
    assert!(result.is_err());
    println!("  ✅ Malicious Markdown rejected by markdown_to_rtf");
    
    let result = secure_markdown_to_rtf(malicious_markdown);
    assert!(result.is_err());
    println!("  ✅ Malicious Markdown rejected by secure_markdown_to_rtf");
    
    // Test valid content acceptance
    let valid_rtf = r"{\rtf1\ansi\deff0 {\fonttbl {\f0 Times New Roman;}} \f0\fs24 Hello World.}";
    let result = rtf_to_markdown(valid_rtf);
    assert!(result.is_ok());
    println!("  ✅ Valid RTF accepted by rtf_to_markdown");
    
    let valid_markdown = "# Hello World\n\nThis is a **test** document.";
    let result = markdown_to_rtf(valid_markdown);
    assert!(result.is_ok());
    println!("  ✅ Valid Markdown accepted by markdown_to_rtf");
    
    println!("  ✅ Conversion functions security tests passed\n");
}

fn test_ffi_security_integration() {
    println!("2. Testing FFI Security Integration");
    println!("----------------------------------");
    
    unsafe {
        // Test malicious RTF through FFI
        let malicious_rtf = CString::new(r"{\rtf1 \objdata 48656C6C6F}").unwrap();
        let mut output_buffer: *mut c_char = ptr::null_mut();
        let mut output_length: c_int = 0;
        
        let result = legacybridge_rtf_to_markdown_unified(
            malicious_rtf.as_ptr(),
            &mut output_buffer,
            &mut output_length,
        );
        
        assert_ne!(result, 0);
        println!("  ✅ Malicious RTF rejected through FFI");
        
        // Test malicious Markdown through FFI
        let malicious_markdown = CString::new("<iframe src='http://evil.com'></iframe>").unwrap();
        let result = legacybridge_markdown_to_rtf_unified(
            malicious_markdown.as_ptr(),
            &mut output_buffer,
            &mut output_length,
        );
        
        assert_ne!(result, 0);
        println!("  ✅ Malicious Markdown rejected through FFI");
        
        // Test size limit enforcement
        let large_content = "x".repeat(11 * 1024 * 1024); // 11MB > 10MB limit
        let large_rtf = CString::new(large_content).unwrap();
        let result = legacybridge_rtf_to_markdown_unified(
            large_rtf.as_ptr(),
            &mut output_buffer,
            &mut output_length,
        );
        
        assert_ne!(result, 0);
        println!("  ✅ Size limit enforced through FFI");
        
        // Test valid content through FFI
        let valid_rtf = CString::new(r"{\rtf1\ansi\deff0 {\fonttbl {\f0 Times New Roman;}} \f0\fs24 Valid content.}").unwrap();
        let result = legacybridge_rtf_to_markdown_unified(
            valid_rtf.as_ptr(),
            &mut output_buffer,
            &mut output_length,
        );
        
        if result == 0 {
            println!("  ✅ Valid content accepted through FFI");
            if !output_buffer.is_null() {
                legacybridge_free_string_unified(output_buffer);
            }
        } else {
            println!("  ⚠️ Valid content unexpectedly rejected");
        }
    }
    
    println!("  ✅ FFI security integration tests passed\n");
}

fn test_malicious_content_rejection() {
    println!("3. Testing Malicious Content Rejection");
    println!("--------------------------------------");
    
    // Test various RTF attack vectors
    let rtf_attacks = vec![
        r"{\rtf1 \field Hello}",
        r"{\rtf1 \fldinst HYPERLINK}",
        r"{\rtf1 \pict\wmetafile8 data}",
        r"{\rtf1 \objemb embedded}",
        r"{\rtf1 \*\generator malicious}",
    ];
    
    for (i, attack) in rtf_attacks.iter().enumerate() {
        let result = rtf_to_markdown(attack);
        assert!(result.is_err());
        println!("  ✅ RTF attack vector {} rejected", i + 1);
    }
    
    // Test various Markdown attack vectors
    let markdown_attacks = vec![
        "<script>alert('xss')</script>",
        "[Click](javascript:alert('xss'))",
        "<img src='x' onerror='alert(1)'>",
        "<iframe src='data:text/html,<script>alert(1)</script>'></iframe>",
        "[File](file:///etc/passwd)",
        "<meta http-equiv='refresh' content='0;url=http://evil.com'>",
    ];
    
    for (i, attack) in markdown_attacks.iter().enumerate() {
        let result = markdown_to_rtf(attack);
        assert!(result.is_err());
        println!("  ✅ Markdown attack vector {} rejected", i + 1);
    }
    
    println!("  ✅ Malicious content rejection tests passed\n");
}

fn test_performance_limits() {
    println!("4. Testing Performance Limits");
    println!("-----------------------------");
    
    // Test excessive RTF nesting
    let deep_nesting = format!("{{\\rtf1 {}}}", "{".repeat(150) + "Hello" + &"}".repeat(150));
    let result = rtf_to_markdown(&deep_nesting);
    assert!(result.is_err());
    println!("  ✅ Excessive RTF nesting rejected");
    
    // Test excessive control words
    let many_controls = format!("{{\\rtf1 {}}}", r"\b ".repeat(15000));
    let result = rtf_to_markdown(&many_controls);
    assert!(result.is_err());
    println!("  ✅ Excessive control words rejected");
    
    // Test excessive table complexity
    let complex_table = format!("{{\\rtf1 {}}}", r"\trowd\cellx1000".repeat(1500));
    let result = rtf_to_markdown(&complex_table);
    assert!(result.is_err());
    println!("  ✅ Complex tables rejected");
    
    // Test excessive Markdown links
    let many_links = format!("# Links\n{}", (0..150).map(|i| format!("[Link {}](http://example.com/{})", i, i)).collect::<Vec<_>>().join("\n"));
    let result = markdown_to_rtf(&many_links);
    assert!(result.is_err());
    println!("  ✅ Excessive links rejected");
    
    // Test excessive line length
    let long_line = format!("# {}", "A".repeat(15000));
    let result = markdown_to_rtf(&long_line);
    assert!(result.is_err());
    println!("  ✅ Long lines rejected");
    
    // Test deep list nesting
    let deep_list = (0..25).map(|i| format!("{}- Item {}", "  ".repeat(i), i)).collect::<Vec<_>>().join("\n");
    let result = markdown_to_rtf(&deep_list);
    assert!(result.is_err());
    println!("  ✅ Deep list nesting rejected");
    
    // Test reasonable content acceptance
    let reasonable_rtf = r"{\rtf1\ansi\deff0 {\fonttbl {\f0 Times New Roman;}} \f0\fs24 This is reasonable content with \b bold \b0 and \i italic \i0 text.}";
    let result = rtf_to_markdown(reasonable_rtf);
    assert!(result.is_ok());
    println!("  ✅ Reasonable RTF content accepted");
    
    let reasonable_markdown = "# Document\n\n## Section 1\n\nThis is **bold** and *italic* text.\n\n- Item 1\n  - Subitem 1\n  - Subitem 2\n- Item 2\n\n[Safe link](https://example.com)";
    let result = markdown_to_rtf(reasonable_markdown);
    assert!(result.is_ok());
    println!("  ✅ Reasonable Markdown content accepted");
    
    println!("  ✅ Performance limits tests passed\n");
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_security_integration() {
        // Test that malicious content is rejected
        let malicious_rtf = r"{\rtf1 \object Hello}";
        assert!(rtf_to_markdown(malicious_rtf).is_err());
        
        let malicious_markdown = "<script>alert('xss')</script>";
        assert!(markdown_to_rtf(malicious_markdown).is_err());
    }

    #[test]
    fn test_valid_content_acceptance() {
        // Test that valid content is accepted
        let valid_rtf = r"{\rtf1\ansi\deff0 Hello World}";
        assert!(rtf_to_markdown(valid_rtf).is_ok());
        
        let valid_markdown = "# Hello World";
        assert!(markdown_to_rtf(valid_markdown).is_ok());
    }

    #[test]
    fn test_size_limits() {
        // Test size limit enforcement
        let large_content = "x".repeat(11 * 1024 * 1024);
        let large_rtf = format!("{{\\rtf1 {}}}", large_content);
        assert!(rtf_to_markdown(&large_rtf).is_err());
    }
}
