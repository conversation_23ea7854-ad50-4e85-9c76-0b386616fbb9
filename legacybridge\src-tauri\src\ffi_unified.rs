// Unified FFI Interface with Consistent Error Handling
// Replaces the old ffi.rs with a unified error handling approach

use std::ffi::{CStr, CString};
use std::os::raw::{c_char, c_int};
use std::ptr;
use std::sync::Mutex;

use crate::conversion::unified_error_system::{UnifiedError, UnifiedResult, IntoUnifiedError};
use crate::conversion::{markdown_to_rtf, rtf_to_markdown, EnhancedInputValidator};

const MAX_INPUT_SIZE: usize = 10 * 1024 * 1024; // 10MB

/// Thread-safe storage for the last error
static LAST_ERROR: Mutex<Option<UnifiedError>> = Mutex::new(None);

/// Set the last error for FFI retrieval
fn set_last_error(error: UnifiedError) {
    if let Ok(mut last_error) = LAST_ERROR.lock() {
        *last_error = Some(error);
    }
}

/// Convert a C string to a Rust String with unified error handling
unsafe fn c_str_to_string_unified(ptr: *const c_char) -> UnifiedResult<String> {
    if ptr.is_null() {
        return Err(UnifiedError::invalid_input("Null pointer provided"));
    }
    
    CStr::from_ptr(ptr)
        .to_str()
        .into_unified_with_context("Converting C string to UTF-8")
        .map(|s| s.to_owned())
}

/// Convert a Rust String to a C string with unified error handling
fn string_to_c_str_unified(s: String) -> UnifiedResult<*mut c_char> {
    CString::new(s)
        .map(|c_string| c_string.into_raw())
        .map_err(|_| UnifiedError::invalid_input("String contains null bytes"))
}

/// Enhanced input validation using the new validation system
fn validate_input_enhanced(content: &str, content_type: &str) -> UnifiedResult<()> {
    let validator = EnhancedInputValidator::new();

    match content_type {
        "RTF" => validator.validate_rtf_content(content),
        "Markdown" => validator.validate_markdown_content(content),
        _ => {
            // Basic validation for unknown types
            if content.len() > MAX_INPUT_SIZE {
                return Err(UnifiedError::resource_limit(
                    "Input size exceeds maximum allowed",
                    "input_size",
                    Some(content.len()),
                    Some(MAX_INPUT_SIZE),
                ));
            }
            Ok(())
        }
    }
}

/// Convert RTF to Markdown with unified error handling
/// 
/// # Parameters
/// - `rtf_content`: Null-terminated C string containing RTF content
/// - `output_buffer`: Pointer to store the output buffer address
/// - `output_length`: Pointer to store the output length
/// 
/// # Returns
/// - 0 on success
/// - Negative error code on failure (use legacybridge_get_last_error for details)
/// 
/// # Safety
/// The caller must free the output buffer using `legacybridge_free_string`
#[no_mangle]
pub unsafe extern "C" fn legacybridge_rtf_to_markdown_unified(
    rtf_content: *const c_char,
    output_buffer: *mut *mut c_char,
    output_length: *mut c_int,
) -> c_int {
    // Validate input parameters
    if output_buffer.is_null() || output_length.is_null() {
        let error = UnifiedError::invalid_input("Output buffer or length pointer is null");
        let code = error.to_ffi_code();
        set_last_error(error);
        return code;
    }

    // Convert and validate input
    let rtf_string = match c_str_to_string_unified(rtf_content) {
        Ok(s) => s,
        Err(error) => {
            let code = error.to_ffi_code();
            set_last_error(error);
            return code;
        }
    };

    if let Err(error) = validate_input_enhanced(&rtf_string, "RTF") {
        let code = error.to_ffi_code();
        set_last_error(error);
        return code;
    }

    // Perform conversion
    match rtf_to_markdown(&rtf_string) {
        Ok(markdown) => {
            match string_to_c_str_unified(markdown.clone()) {
                Ok(c_str) => {
                    *output_buffer = c_str;
                    *output_length = markdown.len() as c_int;
                    0 // Success
                }
                Err(error) => {
                    let code = error.to_ffi_code();
                    set_last_error(error);
                    code
                }
            }
        }
        Err(e) => {
            let error = UnifiedError::conversion_failed_with_formats(
                &e.to_string(),
                "RTF",
                "Markdown"
            );
            let code = error.to_ffi_code();
            set_last_error(error);
            code
        }
    }
}

/// Convert Markdown to RTF with unified error handling
/// 
/// # Parameters
/// - `markdown_content`: Null-terminated C string containing Markdown content
/// - `output_buffer`: Pointer to store the output buffer address
/// - `output_length`: Pointer to store the output length
/// 
/// # Returns
/// - 0 on success
/// - Negative error code on failure (use legacybridge_get_last_error for details)
/// 
/// # Safety
/// The caller must free the output buffer using `legacybridge_free_string`
#[no_mangle]
pub unsafe extern "C" fn legacybridge_markdown_to_rtf_unified(
    markdown_content: *const c_char,
    output_buffer: *mut *mut c_char,
    output_length: *mut c_int,
) -> c_int {
    // Validate input parameters
    if output_buffer.is_null() || output_length.is_null() {
        let error = UnifiedError::invalid_input("Output buffer or length pointer is null");
        let code = error.to_ffi_code();
        set_last_error(error);
        return code;
    }

    // Convert and validate input
    let markdown_string = match c_str_to_string_unified(markdown_content) {
        Ok(s) => s,
        Err(error) => {
            let code = error.to_ffi_code();
            set_last_error(error);
            return code;
        }
    };

    if let Err(error) = validate_input_enhanced(&markdown_string, "Markdown") {
        let code = error.to_ffi_code();
        set_last_error(error);
        return code;
    }

    // Perform conversion
    match markdown_to_rtf(&markdown_string) {
        Ok(rtf) => {
            match string_to_c_str_unified(rtf.clone()) {
                Ok(c_str) => {
                    *output_buffer = c_str;
                    *output_length = rtf.len() as c_int;
                    0 // Success
                }
                Err(error) => {
                    let code = error.to_ffi_code();
                    set_last_error(error);
                    code
                }
            }
        }
        Err(e) => {
            let error = UnifiedError::conversion_failed_with_formats(
                &e.to_string(),
                "Markdown",
                "RTF"
            );
            let code = error.to_ffi_code();
            set_last_error(error);
            code
        }
    }
}

/// Get the last error message (user-safe, unified version)
///
/// # Returns
/// Pointer to null-terminated C string containing the error message
/// Returns null if no error occurred
///
/// # Safety
/// The caller must free the returned string using `legacybridge_free_string_unified`
#[no_mangle]
pub unsafe extern "C" fn legacybridge_get_last_error_unified() -> *mut c_char {
    if let Ok(last_error) = LAST_ERROR.lock() {
        if let Some(ref error) = *last_error {
            let message = error.user_message();
            return string_to_c_str_unified(message).unwrap_or(ptr::null_mut());
        }
    }
    ptr::null_mut()
}

/// Get the last error ID for tracking/support (unified version)
///
/// # Returns
/// Pointer to null-terminated C string containing the error ID
/// Returns null if no error occurred
///
/// # Safety
/// The caller must free the returned string using `legacybridge_free_string_unified`
#[no_mangle]
pub unsafe extern "C" fn legacybridge_get_last_error_id_unified() -> *mut c_char {
    if let Ok(last_error) = LAST_ERROR.lock() {
        if let Some(ref error) = *last_error {
            let error_id = error.error_id().to_string();
            return string_to_c_str_unified(error_id).unwrap_or(ptr::null_mut());
        }
    }
    ptr::null_mut()
}

/// Clear the last error (unified version)
#[no_mangle]
pub extern "C" fn legacybridge_clear_last_error_unified() {
    if let Ok(mut last_error) = LAST_ERROR.lock() {
        *last_error = None;
    }
}

/// Free a string allocated by the unified library
///
/// # Parameters
/// - `ptr`: Pointer to the string to free
///
/// # Safety
/// The pointer must have been allocated by this library
#[no_mangle]
pub unsafe extern "C" fn legacybridge_free_string_unified(ptr: *mut c_char) {
    if !ptr.is_null() {
        let _ = CString::from_raw(ptr);
    }
}

/// Get library version information (unified)
///
/// # Returns
/// Pointer to null-terminated C string containing version info
///
/// # Safety
/// The caller must free the returned string using `legacybridge_free_string_unified`
#[no_mangle]
pub unsafe extern "C" fn legacybridge_get_version_unified() -> *mut c_char {
    let version = format!("LegacyBridge v{} (Unified Error Handling)", env!("CARGO_PKG_VERSION"));
    string_to_c_str_unified(version).unwrap_or(ptr::null_mut())
}

/// Check if the unified library is properly initialized
///
/// # Returns
/// 1 if initialized, 0 otherwise
#[no_mangle]
pub extern "C" fn legacybridge_is_initialized_unified() -> c_int {
    // For now, always return initialized
    // In the future, this could check for proper initialization state
    1
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::ffi::CString;

    #[test]
    fn test_unified_error_conversion() {
        let error = UnifiedError::invalid_input("Test error");
        assert_eq!(error.to_ffi_code(), -1);
        assert!(error.user_message().contains("Test error"));
        assert!(!error.error_id().is_empty());
    }

    #[test]
    fn test_c_string_conversion() {
        let test_string = "Hello, World!";
        let c_string = CString::new(test_string).unwrap();
        
        unsafe {
            let result = c_str_to_string_unified(c_string.as_ptr());
            assert!(result.is_ok());
            assert_eq!(result.unwrap(), test_string);
        }
    }

    #[test]
    fn test_input_size_validation() {
        let small_input = "small";
        assert!(validate_input_enhanced(small_input, "test").is_ok());

        let large_input = "x".repeat(MAX_INPUT_SIZE + 1);
        assert!(validate_input_enhanced(&large_input, "test").is_err());
    }
}
